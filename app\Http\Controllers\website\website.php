<?php

namespace App\Http\Controllers\website;

use App\Http\Controllers\Controller;
use App\Models\BlogCategories;
use App\Models\Blogs;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Cookie;

class website extends Controller
{
       public function setLanguage($lang)
    {
        // Validate language
        $availableLanguages = \App\Models\Language::where('status', 1)->pluck('code')->toArray();
        if (!in_array($lang, $availableLanguages)) {
            $lang = 'en'; // fallback to English
        }

        Session::put('locale', $lang);
        App::setLocale($lang);

        // Store language preference in cookie (30 days)
        Cookie::queue('preferred_language', $lang, 60 * 24 * 30);

        // Get the current path without language prefix
        $currentPath = \App\Helpers\LanguageHelper::getCurrentPathWithoutLanguage();

        // Redirect to the same page with new language prefix
        $newUrl = '/' . $lang . '/' . $currentPath;

        return redirect($newUrl);
    }
    public function HomeView()
    {
    //   $articles = Blogs::where('status', 1)
    //         ->where('type', 'blog')
    //         ->orderBy('created_at', 'desc')
    //         ->take(3)
    //         ->get();
      

       // return view('website.index', compact('articles'));
    
        return view('website.index');
    
    }
     public function AboutView()
    {
    //   $articles = Blogs::where('status', 1)
    //         ->where('type', 'blog')
    //         ->orderBy('created_at', 'desc')
    //         ->take(3)
    //         ->get();
      

        return view('website.about');
    }
    public function blogView()
    {
        // $categories = BlogCategories::get();

        // $articles = Blogs::where('status', 1)
        //     ->where('type', 'blog')->orderBy('created_at', 'desc')
        //     ->paginate(8);

        // $latestArticles = Blogs::where('status', 1)->where('popular', 1)
        //     ->where('type', 'blog')
        //     ->orderBy('created_at', 'desc')
        //     ->take(3)
        //     ->get();
        return view('website.blog');
    }

    public function singleBlogView($slug)
    {
        // $singleArticle = Blogs::where('slug', $slug)->where('status', 1)->first();
        // if (!$singleArticle) {
        //     // If the article is not found, return a 404 error
        //     abort(404);
        // }

        // // Get related articles for sidebar
        // $latestArticles = Blogs::where('status', 1)->where('popular', 1)
        //     ->where('type', 'blog')
        //     ->where('id', '!=', $singleArticle->id)
        //     ->orderBy('created_at', 'desc')
        //     ->take(5)
        //     ->get();

        return view('website.blogDetail');
    }
}