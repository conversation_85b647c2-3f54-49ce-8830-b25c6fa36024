<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\website\website;

// Language switching route (without language prefix) - must be outside middleware group
Route::get('lang/{locale}', [website::class, 'setLanguage'])->name('setlocale');

// Routes with language prefix
Route::middleware(['lang'])->prefix('{locale}')->where(['locale' => '[a-z]{2}'])->group(function () {
    // Home routes
    Route::get('/', [website::class, 'HomeView'])->name('locale.home');
    Route::get('/home', [website::class, 'HomeView'])->name('locale.home.alt');

    // About page
    Route::get('/about', [website::class, 'AboutView'])->name('locale.about');

    // Contact page
    Route::get('/contact', function () {
        return view('website.contact');
    })->name('locale.contact');

    // Services pages
    Route::get('/services', function () {
        return view('website.service');
    })->name('locale.services');

    Route::get('/internal-and-external-shipping', function () {
        return view('website.internalAndExternalShipping');
    })->name('locale.internal-external-shipping');

    Route::get('/storage-and-warehouse-management', function () {
        return view('website.storageAndWarehouseManagement');
    })->name('locale.storage-warehouse-management');

    Route::get('/business-specific-solutions', function () {
        return view('website.businessSpecificSolutions');
    })->name('locale.business-solutions');

    // Blog pages
    Route::get('/blog', [website::class, 'blogView'])->name('locale.blog');
    Route::get('/blog/{slug}', [website::class, 'singleBlogView'])->name('locale.blog.detail');

    // 404 page
    Route::get('/404', function () {
        return view('website.404');
    })->name('locale.404');
});

// Fallback routes without language prefix (redirect to default language)
Route::middleware(['lang'])->group(function () {
    Route::get('/', function () {
        return redirect('/en/', 301);
    });

    Route::get('/home', function () {
        return redirect('/en/home', 301);
    });

    Route::get('/about', function () {
        return redirect('/en/about', 301);
    });

    Route::get('/contact', function () {
        return redirect('/en/contact', 301);
    });

    Route::get('/services', function () {
        return redirect('/en/services', 301);
    });

    Route::get('/internal-and-external-shipping', function () {
        return redirect('/en/internal-and-external-shipping', 301);
    });

    Route::get('/storage-and-warehouse-management', function () {
        return redirect('/en/storage-and-warehouse-management', 301);
    });

    Route::get('/business-specific-solutions', function () {
        return redirect('/en/business-specific-solutions', 301);
    });

    Route::get('/blog', function () {
        return redirect('/en/blog', 301);
    });
});