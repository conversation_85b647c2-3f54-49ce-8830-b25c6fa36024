<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\View;
use App\Http\Controllers\website\website;


Route::middleware(['lang'])->group(function () {
    Route::get('lang/{locale}', [website::class, 'setLanguage'])->name('setlocale');
    Route::get('/', [website::class, 'HomeView']);
    Route::get('/home', [website::class, 'HomeView']);

    Route::get('/about', [website::class, 'AboutView']);

    Route::get('/contact', function () {
        return view('website.contact');
    });
    Route::get('/services', function () {
        return view('website.service');
    });
    Route::get('/internal-and-external-shipping', function () {
        return view('website.internalAndExternalShipping');
    });
    Route::get('/storage-and-warehouse-management', function () {
        return view('website.storageAndWarehouseManagement');
    });
    Route::get('/business-specific-solutions', function () {
        return view('website.businessSpecificSolutions');
    });

    Route::get('/blog', [website::class, 'blogView']);

    Route::get('/blog/{slug}', [website::class, 'singleBlogView'])->name('blog.detail');


    Route::get('/404', function () {
        return view('website.404');
    });
});