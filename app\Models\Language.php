<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Language extends Model
{
    use HasFactory;

    protected $table = 'languages';

    protected $fillable = [
        'name',
        'code',
        'status',
    ];

    // Query scopes for better performance
    public function scopeActive($query)
    {
        return $query->where('status', 1);
    }

    public function scopeByCode($query, $code)
    {
        return $query->where('code', $code);
    }

    // Static methods for common queries
    public static function getActive()
    {
        return static::active()->select('id', 'name', 'code')->get();
    }

    public static function findByCode($code)
    {
        return static::active()->byCode($code)->select('id', 'name', 'code')->first();
    }
}
