:root {
    --body: #fff;
    --black: #000;
    --white: #fff;
    --theme: #CA1F27;
    --theme2: #F7C600;
    --theme3: #1f425d;
    --header: #2a2833;
    --base: #00813D;
    --text: #767676;
    --text2: #bfbfbf;
    --border: #D9D9D9;
    --border2: #373737;
    --button: #1C2539;
    --button2: #030734;
    --ratting: #FF9F0D;
    --bg: #f5f5f8;
    --bg2: #DF0A0A0D;
    --color-gradient-1: linear-gradient(309deg, rgba(202, 31, 39, 1) 17%, rgba(247, 207, 0, 1) 70%);
    ---color-gradient-2: linear-gradient(309deg, rgba(247, 207, 0, 1) 14%, rgba(202, 31, 39, 1) 72%);
    ---box-shadow: 0px 0px 60px 0px rgba(4, 23, 26, 0.08);
}

.theme-btn {
    background-color: var(--theme);
    color: var(--white);
    display: inline-block;
    font-size: 14px;
    font-weight: 700;
    padding: 18px 50px;
    border-radius: 0;
    letter-spacing: 0.2px;
    text-transform: uppercase;
    transition: all 0.3s ease-in-out;
    position: relative;
    z-index: 1;
}

.theme-btn::before {
    content: "";
    background-color: var(--header);
    width: 0;
    height: 50%;
    position: absolute;
    top: 0;
    left: 0;
    transition: all 0.3s cubic-bezier(0.785, 0.135, 0.15, 0.86);
    z-index: -1;
}

.theme-btn::after {
    content: "";
    background-color: var(--header);
    width: 0;
    height: 50%;
    position: absolute;
    bottom: 0;
    right: 0;
    transition: all 0.3s cubic-bezier(0.785, 0.135, 0.15, 0.86);
    z-index: -1;
}

.theme-btn:hover {
    color: var(--white);
}

.theme-btn:hover::before,
.theme-btn:hover::after {
    width: 100%;
}

.theme-btn.hover-white:hover {
    color: var(--header);
}

.theme-btn.hover-white:hover::before,
.theme-btn.hover-white:hover::after {
    background-color: var(--white);
}

.theme-btn.header-color {
    background-color: var(--header);
    background-image: inherit;
}

.theme-btn.header-color::before,
.theme-btn.header-color::after {
    background-color: var(--theme);
}

.theme-btn.header-color-2 {
    background-color: var(--header);
    background-image: inherit;
}

.theme-btn.header-color-2::before,
.theme-btn.header-color-2::after {
    background-color: var(--white);
}

.theme-btn.header-color-2:hover {
    color: var(--header);
}

.theme-btn.bg-white {
    background-color: var(--white);
    color: var(--header);
    background-image: initial;
}

.theme-btn.bg-white:hover {
    color: var(--white);
}

.theme-btn.bg-white:hover::before,
.theme-btn.bg-white:hover::after {
    background-color: var(--header);
}

.theme-btn.bg-second {
    background-color: var(--theme2);
    color: var(--black);
    background-image: initial;
}

.theme-btn.bg-second:hover {
    color: var(--white);
}

.theme-btn.bg-second:hover::before,
.theme-btn.bg-second:hover::after {
    background-color: var(--theme);
}

@media (max-width: 767px) {
    .theme-btn {
        padding: 14px 32px;
    }
}

@media (max-width: 575px) {
    .theme-btn {
        padding: 12px 30px;
        font-size: 13px;
    }
}

.link-btn {
    text-transform: uppercase;
    font-size: 16px;
    color: var(--header);
    font-weight: 600;
    display: inline-block;
    background-image: linear-gradient(var(--theme), var(--theme));
    background-position: 0 95%;
    background-repeat: no-repeat;
    background-size: 0% 2px;
}

.link-btn i {
    margin-left: 10px;
    font-size: 16px;
    transition: all 0.4s ease-in-out;
    opacity: 0;
}

.link-btn:hover {
    color: var(--theme);
    background-size: 100% 1px;
}

.link-btn:hover i {
    color: var(--theme);
    opacity: 1;
}

/* --------------------------------------------
    Template Default Fonts & Fonts Styles
 ---------------------------------------------- */

body {
    font-family: "DM Sans", sans-serif;
    font-size: 16px;
    font-weight: normal;
    line-height: 28px;
    color: var(--text);
    background-color: var(--white);
    padding: 0;
    margin: 0;
    overflow-x: hidden;
}

ul {
    padding: 0;
    margin: 0;
    list-style: none;
}

button {
    border: none;
    background-color: transparent;
    padding: 0;
}

input:focus {
    color: var(--white);
    outline: none;
}

input {
    color: var(--white);
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-family: "Kanit", sans-serif;
    margin: 0px;
    padding: 0;
    color: var(--header);
    text-transform: capitalize;
    transition: all 0.4s ease-in-out;
}

h1 {
    font-size: 90px;
    font-weight: 500;
    line-height: 100%;
}

h2 {
    font-size: 45px;
    line-height: 110%;
    font-weight: 600;
}

@media (max-width: 991px) {
    h2 {
        font-size: 42px;
    }
}

@media (max-width: 575px) {
    h2 {
        font-size: 32px;
    }
}

h3 {
    font-size: 24px;
    font-weight: 600;
}

@media (max-width: 575px) {
    h3 {
        font-size: 20px;
    }
}

h4 {
    font-size: 22px;
    font-weight: 500;
}

h5 {
    font-size: 20px;
    font-weight: 600;
}

h6 {
    font-size: 16px;
    font-weight: 600;
    line-height: 145%;
}

a {
    text-decoration: none;
    outline: none !important;
    cursor: pointer;
    color: var(--header);
    transition: all 0.4s ease-in-out;
}

p {
    margin: 0px;
    transition: all 0.4s ease-in-out;
}

.preloader {
    align-items: center;
    cursor: default;
    display: flex;
    height: 100%;
    justify-content: center;
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    z-index: 9999999;
}

.preloader .animation-preloader {
    z-index: 1000;
}

.preloader .animation-preloader .spinner {
    animation: spinner 1s infinite linear;
    border-radius: 50%;
    border: 3px solid rgba(0, 0, 0, 0.2);
    border-top-color: var(--theme);
    height: 9em;
    margin: 0 auto 3.5em auto;
    width: 9em;
}

@media (max-width: 767px) {
    .preloader .animation-preloader .spinner {
        width: 7.5em;
        height: 7.5em;
        margin: 0 auto 1.5em auto;
    }
}

.preloader .animation-preloader .txt-loading {
    font: bold 5em "Kanit", sans-serif, "DM Sans", sans-serif;
    text-align: center;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}

@media (max-width: 767px) {
    .preloader .animation-preloader .txt-loading {
        font-size: 2.5em;
    }
}

.preloader .animation-preloader .txt-loading .letters-loading {
    color: var(--theme);
    position: relative;
}

.preloader .animation-preloader .txt-loading .letters-loading:nth-child(2):before {
    animation-delay: 0.2s;
}

.preloader .animation-preloader .txt-loading .letters-loading:nth-child(3):before {
    animation-delay: 0.4s;
}

.preloader .animation-preloader .txt-loading .letters-loading:nth-child(4):before {
    animation-delay: 0.6s;
}

.preloader .animation-preloader .txt-loading .letters-loading:nth-child(5):before {
    animation-delay: 0.8s;
}

.preloader .animation-preloader .txt-loading .letters-loading:nth-child(6):before {
    animation-delay: 1s;
}

.preloader .animation-preloader .txt-loading .letters-loading:nth-child(7):before {
    animation-delay: 1.2s;
}

.preloader .animation-preloader .txt-loading .letters-loading:nth-child(8):before {
    animation-delay: 1.4s;
}

.preloader .animation-preloader .txt-loading .letters-loading::before {
    animation: letters-loading 4s infinite;
    color: var(--theme2);
    content: attr(data-text-preloader);
    left: 0;
    opacity: 0;
    font-family: "Kanit", sans-serif;
    position: absolute;
    top: -3px;
    transform: rotateY(-90deg);
}

.preloader p {
    font-size: 15px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 8px;
    color: var(--theme);
}

.preloader .loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    font-size: 0;
    z-index: 1;
    pointer-events: none;
}

.preloader .loader .row {
    height: 100%;
}

.preloader .loader .loader-section {
    padding: 0px;
}

.preloader .loader .loader-section .bg {
    background-color: #fff;
    height: 100%;
    left: 0;
    width: 100%;
    transition: all 800ms cubic-bezier(0.77, 0, 0.175, 1);
}

.preloader.loaded .animation-preloader {
    opacity: 0;
    transition: 0.3s ease-out;
}

.preloader.loaded .loader-section .bg {
    width: 0;
    transition: 0.7s 0.3s allcubic-bezier(0.1, 0.1, 0.1, 1);
}

.search-wrap {
    width: 100%;
    height: 100%;
    overflow: hidden;
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 999999;
    background-color: rgba(255, 255, 255, 0.9);
}

.search-wrap .search-inner {
    position: relative;
    width: 100%;
    height: 100%;
}

.search-wrap .search-cell {
    position: absolute;
    top: 50%;
    width: 100%;
    transform: translateY(-50%);
}

.search-wrap .search-field-holder {
    width: 50%;
    margin: auto;
    position: relative;
    animation: slideInUp 0.3s;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .search-wrap .search-field-holder {
        width: 70%;
    }
}

@media (max-width: 575px) {
    .search-wrap .search-field-holder {
        width: 80%;
    }
}

.search-wrap .main-search-input {
    width: 100%;
    height: 70px;
    border: 0;
    padding: 0 50px;
    text-transform: capitalize;
    background: transparent;
    font-size: 25px;
    color: var(--header);
    border-bottom: 2px solid var(--header);
    text-align: center;
    letter-spacing: 2px;
}

@media (max-width: 575px) {
    .search-wrap .main-search-input {
        height: 50px;
        padding: 0 0;
        line-height: 50px;
        font-size: 18px;
    }
}

.search-wrap input.form-control,
.search-wrap input.form-control:focus {
    background-color: var(--header);
}

input.main-search-input::-moz-placeholder {
    color: var(--header);
    opacity: 1;
    font-size: 25px;
}

input.main-search-input::placeholder {
    color: var(--header);
    opacity: 1;
    font-size: 25px;
}

@media (max-width: 575px) {
    input.main-search-input::-moz-placeholder {
        font-size: 18px;
    }
    input.main-search-input::placeholder {
        font-size: 18px;
    }
}

.search-close {
    position: absolute;
    top: 50px;
    right: 50px;
    font-size: 30px;
    color: var(--theme);
    cursor: pointer;
}

.scroll-up {
    cursor: pointer;
    display: block;
    border-radius: 50px;
    z-index: 99;
    opacity: 0;
    visibility: hidden;
    transform: translateY(15px);
    position: fixed;
    right: 25px;
    bottom: 35px;
    height: 50px;
    width: 50px;
    transition: all 0.4s ease-in-out;
}

.scroll-up::after {
    position: absolute;
    font-family: "Font Awesome 5 Pro";
    content: "\f176";
    text-align: center;
    line-height: 50px;
    font-weight: 700;
    font-size: 18px;
    color: var(--theme);
    left: 0;
    top: 0;
    height: 50px;
    width: 50px;
    cursor: pointer;
    display: block;
    z-index: 1;
    transition: all 0.4s ease-in-out;
}

.scroll-up svg path {
    fill: none;
}

.scroll-up svg.scroll-circle path {
    stroke: var(--theme);
    stroke-width: 4px;
    box-sizing: border-box;
    transition: all 0.4s ease-in-out;
}

.scroll-up.active-scroll {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.mouse-cursor {
    position: fixed;
    left: 0;
    top: 0;
    pointer-events: none;
    border-radius: 50%;
    transform: translateZ(0);
    visibility: hidden;
}

@media (max-width: 767px) {
    .mouse-cursor {
        display: none;
    }
}

.cursor-inner {
    width: 6px;
    height: 6px;
    z-index: 10000001;
    background-color: var(--theme);
    transition: width 0.3s ease-in-out, height 0.3s ease-in-out, margin 0.3s ease-in-out, opacity 0.3s ease-in-out;
}

.cursor-inner.cursor-hover {
    margin-left: -35px;
    margin-top: -35px;
    width: 70px;
    height: 70px;
    background-color: var(--theme);
    opacity: 0.3;
}

.cursor-outer {
    margin-left: -12px;
    margin-top: -12px;
    width: 30px;
    height: 30px;
    border: 1px solid var(--theme);
    box-sizing: border-box;
    z-index: 10000000;
    opacity: 0.5;
    transition: all 0.08s ease-out;
}

.cursor-outer.cursor-hover {
    opacity: 0;
}

.section-title {
    position: relative;
    z-index: 99;
    margin-bottom: 30px;
    margin-top: -6px;
}

@media (max-width: 767px) {
    .section-title {
        margin-bottom: 0;
    }
}

.section-title span {
    color: var(--theme);
    margin-bottom: 10px;
    display: inline-block;
    font-weight: 600;
    text-transform: capitalize;
}

.section-title h2 {
    text-transform: capitalize;
}

.section-title.style-2 h2 {
    text-transform: capitalize;
}

.section-title.style-2 h2 span {
    font-weight: 600;
    margin-bottom: 0;
    color: var(--header);
    position: relative;
}

.section-title.style-2 h2 span::before {
    position: absolute;
    bottom: 0;
    right: -25px;
    content: "";
    background-image: url(/website/img/line-shape.webp);
    background-repeat: no-repeat;
    background-size: cover;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.center {
    text-align: center;
    margin: 0 auto;
}

.section-bg {
    background-color: var(--bg);
}

.section-padding {
    padding: 120px 0;
}

@media (max-width: 1199px) {
    .section-padding {
        padding: 100px 0;
    }
}

@media (max-width: 991px) {
    .section-padding {
        padding: 80px 0;
    }
}

@keyframes rippleOne {
    70% {
        box-shadow: 0 0 0 40px rgba(244, 68, 56, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(244, 68, 56, 0);
    }
}

@keyframes cir36 {
    100% {
        transform: rotate(360deg);
    }
}

@keyframes rounded {
    50% {
        transform: rotate(15deg);
    }
}

@keyframes rounded-2 {
    50% {
        transform: rotate(5deg);
    }
}

@keyframes rotate {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.animation__rotate {
    animation: rotate 40s linear infinite;
}

@keyframes spinner {
    to {
        transform: rotateZ(360deg);
    }
}

@keyframes letters-loading {
    0%,
    75%,
    100% {
        opacity: 0;
        transform: rotateY(-90deg);
    }
    25%,
    50% {
        opacity: 1;
        transform: rotateY(0deg);
    }
}

@keyframes loaderspin {
    0% {
        transform: translate(-50%, -50%) rotate(0deg);
    }
    100% {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

@keyframes tpswing {
    0% {
        transform: rotate(20deg);
    }
    100% {
        transform: rotate(0deg);
    }
}

@keyframes width {
    0% {
        width: 0%;
    }
    100% {
        width: 100%;
    }
}

@keyframes loaderpulse {
    0% {
        transform: scale(1);
    }
    100% {
        transform: scale(1.2);
    }
}

.float-bob-y {
    animation-name: float-bob-y;
    animation-duration: 3s;
    animation-iteration-count: infinite;
    animation-timing-function: linear;
}

@keyframes float-bob-y {
    0% {
        transform: translateY(-30px);
    }
    50% {
        transform: translateY(-10px);
    }
    100% {
        transform: translateY(-30px);
    }
}

@keyframes top-image-bounce {
    0% {
        transform: translateX(-8px);
    }
    50% {
        transform: translateX(12px);
    }
    100% {
        transform: translateX(-8px);
    }
}

@keyframes top-image-bounce-2 {
    0% {
        transform: translatey(-8px);
    }
    50% {
        transform: translatey(12px);
    }
    100% {
        transform: translatey(-8px);
    }
}

@keyframes toggle {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}

@keyframes moveCursor1 {
    from {
        transform: scale(1);
    }
    to {
        transform: scale(0.8);
    }
}

@keyframes moveCursor2 {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.5);
    }
    100% {
        transform: scale(1);
        opacity: 0;
    }
}

.header-top {
    background-color: var(--theme);
    padding: 10px 0;
}

@media (max-width: 991px) {
    .header-top {
        display: none;
    }
}

.header-top .container-fluid {
    padding: 0 50px;
}

@media (max-width: 470px) {
    .header-top .container-fluid {
        padding: 0 15px;
    }
}

.header-top-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.header-top-wrapper ul {
    display: flex;
    align-items: center;
    gap: 85px;
}

.header-top-wrapper ul li {
    color: var(--white);
}

.header-top-wrapper ul li i {
    color: var(--white);
}

.header-top-wrapper ul li a {
    color: var(--white);
}

.header-top-wrapper .social-icon span {
    color: var(--white);
}

.header-top-wrapper .social-icon i {
    color: var(--white);
    transition: all 0.4s ease-in-out;
}

.header-top-wrapper .social-icon i:hover {
    color: var(--theme);
}

.top-bar-header {
    background: rgba(var(--theme), 0.2);
    padding: 9px 0px;
}

@media (max-width: 767px) {
    .top-bar-header {
        padding: 12px 0px;
    }
    .top-bar-header .d-flex.justify-content-end {
        justify-content: center !important;
    }
}

.top-bar-header .top-welcome-text {
    text-transform: capitalize;
}

.top-bar-header .top-welcome-text p {
    font-size: 16px;
    font-weight: 500;
    line-height: 1;
}

@media (max-width: 767px) {
    .top-bar-header .top-welcome-text p {
        font-size: 14px;
    }
}

.top-bar-header .social-links {
    margin-left: 30px;
    margin-right: 10px;
}

.top-bar-header .social-links a {
    display: inline-block;
    margin: 0px 6px;
    color: var(--theme);
}

.top-bar-header .social-links a:hover {
    color: var(--theme2);
}

.top-bar-header .search-box {
    overflow: hidden;
    position: relative;
    border-radius: 30px;
    border: 1px solid #f9f9f9;
    box-sizing: border-box;
}

.top-bar-header .search-box form {
    position: relative;
}

.top-bar-header .search-box form input {
    height: 36px;
    width: 100%;
    padding: 0px 15px;
    font-size: 14px;
    color: var(--theme);
}

.top-bar-header .search-box form button {
    background: transparent;
    position: absolute;
    right: 10px;
    color: var(--theme);
    border: 0px;
    top: 0;
    line-height: 36px;
    font-size: 14px;
    transition: all 0.4s ease-in-out;
}

.top-bar-header .search-box form button:hover {
    color: var(--theme2);
}

.header-tops-section {
    background-color: var(--theme3);
    padding: 10px 0;
    position: relative;
    z-index: 99;
}

@media (max-width: 991px) {
    .header-tops-section {
        display: none;
    }
}

.header-tops-section::before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    content: "";
    background-color: var(--theme);
    z-index: -1;
    left: 81%;
}

@media (max-width: 1899px) {
    .header-tops-section::before {
        display: none;
    }
}

.header-tops-section .container-fluid {
    padding: 0 130px;
}

@media (max-width: 1399px) {
    .header-tops-section .container-fluid {
        padding: 0 50px;
    }
}

.header-tops-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.header-tops-wrapper .contact-list {
    display: flex;
    align-items: center;
    gap: 25px;
}

.header-tops-wrapper .contact-list li {
    font-size: 15px;
    color: var(--white);
}

.header-tops-wrapper .contact-list li a {
    color: var(--white);
}

.header-tops-wrapper .contact-list li i {
    color: var(--white);
    margin-right: 10px;
}

.header-tops-wrapper .top-right {
    display: flex;
    align-items: center;
    gap: 30px;
}

.header-tops-wrapper .top-right .social-icon {
    gap: 15px;
}

.header-tops-wrapper .top-right .social-icon span {
    color: var(--white);
    font-weight: 500;
}

.header-tops-wrapper .top-right .social-icon a {
    color: var(--white);
}

.header-tops-wrapper .top-right .flag-wrap {
    position: relative;
    width: 160px;
}

.header-tops-wrapper .top-right .flag-wrap .nice-select {
    padding: 0 0 0 60px;
    background: transparent;
    border: none;
    text-align: center;
    margin: 0 auto;
    position: relative;
    z-index: 999;
    border: none !important;
    padding-bottom: 0 !important;
}

.header-tops-wrapper .top-right .flag-wrap .nice-select::after {
    right: 23px;
    border-bottom: 2px solid transparent;
    border-right: 2px solid transparent;
    width: 10px;
    height: 10px;
    top: 15px;
    border-bottom: 2px solid transparent;
    border-color: var(--theme);
    border-right: 2px solid theme-color;
}

.header-tops-wrapper .top-right .flag-wrap .nice-select span {
    font-size: 16px;
    font-weight: 500;
    text-transform: capitalize;
    color: var(--white);
}

.header-tops-wrapper .top-right .flag-wrap .flag {
    position: absolute;
    top: 0;
    left: 15px;
    z-index: 1;
}

@media (max-width: 767px) {
    .header-tops-wrapper .top-right .flag-wrap .flag {
        display: none;
    }
}

.header-tops-wrapper .top-right .flag-wrap .flag img {
    width: 100%;
    height: 100%;
    border: 4px solid var(--white);
    border-radius: 50%;
}

.header-main {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 0;
}

@media (max-width: 991px) {
    .header-main {
        padding: 15px 0px;
    }
}

.header-main .main-menu ul {
    margin-bottom: 0;
}

.header-main .main-menu ul li {
    position: relative;
    list-style: none;
    display: inline-block;
    margin-inline-end: 50px;
}

.header-main .main-menu ul li:last-child {
    margin-inline-end: 0;
}

.header-main .main-menu ul li a {
    display: inline-block;
    font-size: 16px;
    font-weight: 500;
    color: var(--header);
    padding: 30px 0;
    text-align: left;
    position: relative;
    text-transform: capitalize;
    transition: all 0.4s ease-in-out;
}

.header-main .main-menu ul li a i {
    font-size: 15px;
    font-weight: 600;
    margin-left: 5px;
}

.header-main .main-menu ul li a:hover {
    color: var(--theme) !important;
}

.header-main .main-menu ul li .submenu {
    position: absolute;
    top: 100%;
    inset-inline-start: 0;
    min-width: 240px;
    background: var(--white);
    z-index: 99999;
    visibility: hidden;
    opacity: 0;
    transform-origin: top center;
    color: var(--header);
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
    -webkit-box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
    -moz-box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
    transform: translateY(10px);
    transition: all 0.4s ease-in-out;
}

.header-main .main-menu ul li .submenu li {
    display: block;
    width: 100%;
    margin: 0;
    padding: 0;
}

.header-main .main-menu ul li .submenu li a {
    position: relative;
    z-index: 11;
    font-size: 16px;
    font-weight: 500;
    color: var(--header);
    padding: 0 25px;
    padding-bottom: 11px;
    padding-top: 11px;
    width: 100%;
    border-bottom: 1px solid #eeeeee;
}

.header-main .main-menu ul li .submenu li:last-child a {
    border: none;
}

.header-main .main-menu ul li .submenu li .submenu {
    inset-inline-start: 100%;
    top: 0;
    visibility: hidden;
    opacity: 0;
}

.header-main .main-menu ul li .submenu li:hover>a {
    background: var(--theme);
    color: var(--white) !important;
}

.header-main .main-menu ul li .submenu li:hover>a::after {
    color: var(--theme);
}

.header-main .main-menu ul li .submenu li:hover>.submenu {
    transform: translateY(1);
    visibility: visible;
    opacity: 1;
}

.header-main .main-menu ul li .submenu li.has-dropdown>a::after {
    position: absolute;
    top: 50%;
    inset-inline-end: 25px;
    transform: translateY(-50%);
    color: var(--theme);
}

.header-main .main-menu ul li .has-homemenu {
    width: 1000px;
    padding: 30px 30px 10px 30px;
    opacity: 0;
    left: -250px;
    visibility: hidden;
    padding: 30px 30px 10px 30px;
}

.header-main .main-menu ul li .has-homemenu .homemenu {
    position: relative;
}

.header-main .main-menu ul li .has-homemenu .homemenu .homemenu-thumb {
    position: relative;
}

.header-main .main-menu ul li .has-homemenu .homemenu .homemenu-thumb .demo-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60%;
    gap: 10px;
    display: flex;
    justify-content: center;
    flex-direction: column;
    opacity: 0;
    visibility: hidden;
    transition: all 0.4s ease-in-out;
    margin-top: 20px;
}

.header-main .main-menu ul li .has-homemenu .homemenu .homemenu-thumb .demo-button .theme-btn {
    padding: 10px 30px;
    color: var(--white) !important;
    width: initial;
    font-size: 14px;
    text-align: center;
}

.header-main .main-menu ul li .has-homemenu .homemenu .homemenu-thumb .demo-button .theme-btn:hover {
    color: var(--white) !important;
}

.header-main .main-menu ul li .has-homemenu .homemenu .homemenu-thumb::before {
    background: linear-gradient(to bottom, rgba(99, 92, 92, 0) 0%, #292930 100%);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    overflow: hidden;
    opacity: 0;
    transition: all 0.3s ease-in-out;
    content: "";
}

.header-main .main-menu ul li .has-homemenu .homemenu .homemenu-thumb:hover::before {
    visibility: visible;
    opacity: 1;
}

.header-main .main-menu ul li .has-homemenu .homemenu .homemenu-thumb:hover .demo-button {
    opacity: 1;
    visibility: visible;
    margin-top: 0;
}

.header-main .main-menu ul li .has-homemenu .homemenu .homemenu-thumb:hover .homemenu-btn {
    opacity: 1;
    visibility: visible;
    bottom: 50%;
    transform: translateY(50%);
}

.header-main .main-menu ul li .has-homemenu .homemenu .homemenu-thumb img {
    width: 100%;
}

.header-main .main-menu ul li .has-homemenu .homemenu .homemenu-title {
    text-align: center;
    margin: 15px auto;
    display: inline-block;
    font-size: 16px;
}

.header-main .main-menu ul li:hover>a {
    color: var(--theme);
}

.header-main .main-menu ul li:hover>a::after {
    color: var(--theme);
}

.header-main .main-menu ul li:hover>.submenu {
    visibility: visible;
    opacity: 1;
    transform: translateY(0px);
}

.header-1 {
    position: absolute;
    z-index: 99;
    overflow-x: clip;
    top: 0;
    left: 0;
    width: 100%;
    border-bottom: 1px solid #36343c;
}

.header-1 .container-fluid {
    padding: 0 50px;
}

@media (max-width: 470px) {
    .header-1 .container-fluid {
        padding: 0 15px;
    }
}

@media (max-width: 1199px) {
    .header-1 .mega-menu-wrapper .header-main {
        gap: 20px;
    }
}

.header-1 .mega-menu-wrapper .header-main .header-logo-2 {
    display: none;
}

@media (max-width: 1199px) {
    .header-1 .mega-menu-wrapper .header-main .header-left .mean__menu-wrapper .main-menu li {
        margin-inline-end: 20px;
    }
}

.header-1 .mega-menu-wrapper .header-main .header-left .mean__menu-wrapper .main-menu li a {
    color: var(--white);
}

.header-1 .mega-menu-wrapper .header-main .header-left .mean__menu-wrapper .main-menu li .submenu li a {
    color: var(--header);
}

.header-1 .mega-menu-wrapper .header-main .header-right,.header-right {
    gap: 30px;
}

.header-1 .mega-menu-wrapper .header-main .header-right .search-icon {
    color: var(--white);
}

.header-1 .mega-menu-wrapper .header-main .header-right .cart-icon {
    color: var(--white);
}

@media (max-width: 1199px) {
    .header-1 .mega-menu-wrapper .header-main .header-right .cart-icon {
        display: none;
    }
}

.header-1 .mega-menu-wrapper .header-main .header-right .header__hamburger .sidebar__toggle {
    color: var(--white);
}

.header-2 .container-fluid {
    padding: 0 50px;
}

@media (max-width: 470px) {
    .header-2 .container-fluid {
        padding: 0 15px;
    }
}

.header-2 .mega-menu-wrapper .header-main .header-left {
    display: flex;
    align-items: center;
    gap: 140px;
    justify-content: space-between;
}

@media (max-width: 1399px) {
    .header-2 .mega-menu-wrapper .header-main .header-left {
        gap: 35px;
    }
}

@media (max-width: 1199px) {
    .header-2 .mega-menu-wrapper .header-main .header-left {
        gap: 30px;
    }
}

@media (max-width: 1399px) {
    .header-2 .mega-menu-wrapper .header-main .header-left ul li {
        margin-inline-end: 25px;
    }
}

.header-2 .mega-menu-wrapper .header-main .header-right {
    gap: 30px;
}

@media (max-width: 1399px) {
    .header-2 .mega-menu-wrapper .header-main .header-right {
        gap: 20px;
    }
}

@media (max-width: 1600px) {
    .header-2 .mega-menu-wrapper .header-main .header-right .header-button {
        display: none;
    }
}

.header-2 .mega-menu-wrapper .header-main .header-right .cart-icon {
    color: var(--header);
}

.header-2 .mega-menu-wrapper .header-main .header-right .header__hamburger .sidebar__toggle {
    color: var(--header);
}

.header-3 {
    position: relative;
    background-color: var(--white);
    box-shadow: var(---box-shadow);
    z-index: 999;
}

.header-3::before {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    height: 100%;
    width: 125px;
    content: "";
    background-color: var(--theme);
    z-index: -1;
}

@media (max-width: 1899px) {
    .header-3::before {
        display: none !important;
    }
}

.header-3 .container-fluid {
    padding: 0 130px;
}

@media (max-width: 1399px) {
    .header-3 .container-fluid {
        padding: 0 50px;
    }
}

@media (max-width: 991px) {
    .header-3 .container-fluid {
        padding: 0 40px;
    }
}

@media (max-width: 575px) {
    .header-3 .container-fluid {
        padding: 0 15px;
    }
}

@media screen and (max-width: 1199px) {
    .header-3 .header-main .main-menu ul li {
        margin-inline-end: 25px;
    }
}

.header-3 .header-main .main-menu ul li .has-homemenu {
    left: -400px;
}

@media (max-width: 1600px) {
    .header-3 .header-main .main-menu ul li .has-homemenu {
        left: -300px;
    }
}

.header-3 .header-main .header-right {
    gap: 50px;
}

@media (max-width: 991px) {
    .header-3 .header-main .header-right {
        gap: 25px;
    }
}

.header-3 .header-main .header-right .search-icon {
    color: var(--header);
}

@media (max-width: 1199px) {
    .header-3 .header-main .header-right .search-icon {
        display: none;
    }
}

@media (max-width: 991px) {
    .header-3 .header-main .header-right .search-icon {
        display: block;
    }
}

.header-3 .header-main .header-right .header-button {
    margin-right: 50px;
}

@media (max-width: 1600px) {
    .header-3 .header-main .header-right .header-button {
        display: none;
    }
}

.header-3 .header-main .header-right .header__hamburger {
    margin-right: -70px;
}

@media (max-width: 1899px) {
    .header-3 .header-main .header-right .header__hamburger {
        margin-right: 0;
    }
}

.header-3 .header-main .header-right .header__hamburger .sidebar__toggle {
    font-size: 24px;
    color: var(--white);
}

@media (max-width: 1899px) {
    .header-3 .header-main .header-right .header__hamburger .sidebar__toggle {
        color: var(--theme);
    }
}

.sidebar__toggle {
    cursor: pointer;
}

.sticky {
    position: fixed !important;
    top: 0 !important;
    left: 0;
    width: 100%;
    z-index: 100;
    transition: all 0.9s;
    background-color: var(--white);
    box-shadow: var(---box-shadow);
    animation: 500ms ease-in-out 0s normal none 1 running fadeInDown;
}

.sticky.header-1 {
    border-bottom: none;
}

.sticky.header-1 .mega-menu-wrapper .header-main {
    padding: 0px 0px;
}

@media (max-width: 991px) {
    .sticky.header-1 .mega-menu-wrapper .header-main {
        padding: 12px 0px !important;
    }
}

.sticky.header-1 .mega-menu-wrapper .header-main .header-logo-2 {
    display: block;
}

.sticky.header-1 .mega-menu-wrapper .header-main .header-logo {
    display: none;
}

.sticky.header-1 .mega-menu-wrapper .header-main .header-left .mean__menu-wrapper .main-menu li a {
    color: var(--header);
}

.sticky.header-1 .mega-menu-wrapper .header-main .header-left .mean__menu-wrapper .main-menu li .submenu li a {
    color: var(--header);
}

.sticky.header-1 .mega-menu-wrapper .header-main .header-right {
    gap: 30px;
}

.sticky.header-1 .mega-menu-wrapper .header-main .header-right .search-icon {
    color: var(--header);
}

.sticky.header-1 .mega-menu-wrapper .header-main .header-right .cart-icon {
    color: var(--header);
}

.sticky.header-1 .mega-menu-wrapper .header-main .header-right .header__hamburger .sidebar__toggle {
    color: var(--header);
}

.offcanvas__info {
    background: var(--white) none repeat scroll 0 0;
    border-left: 2px solid var(--theme);
    position: fixed;
    right: 0;
    top: 0;
    width: 400px;
    height: 100%;
    transform: translateX(calc(100% + 80px));
    transition: transform 0.45s ease-in-out, opacity 0.45s ease-in-out;
    z-index: 9999;
    overflow-y: scroll;
    overscroll-behavior-y: contain;
    scrollbar-width: none;
}

.offcanvas__info::-webkit-scrollbar {
    display: none;
}

.offcanvas__info.info-open {
    opacity: 1;
    transform: translateX(0);
}

.offcanvas__wrapper {
    position: relative;
    height: 100%;
    padding: 30px 30px;
}

.offcanvas__wrapper .offcanvas__content .text {
    color: var(--text);
}

.offcanvas__wrapper .offcanvas__content .offcanvas__close {
    width: 45px;
    height: 45px;
    line-height: 45px;
    text-align: center;
    border-radius: 50%;
    background-color: var(--theme);
    position: relative;
    z-index: 9;
    cursor: pointer;
}

.offcanvas__wrapper .offcanvas__content .offcanvas__close i {
    color: var(--white);
}

.offcanvas__wrapper .offcanvas__content .offcanvas__contact {
    margin-top: 20px;
}

.offcanvas__wrapper .offcanvas__content .offcanvas__contact ul {
    margin-top: 20px;
}

.offcanvas__wrapper .offcanvas__content .offcanvas__contact ul li {
    font-size: 16px;
    font-weight: 500;
    text-transform: capitalize;
}

.offcanvas__wrapper .offcanvas__content .offcanvas__contact ul li:not(:last-child) {
    margin-bottom: 15px;
}

.offcanvas__wrapper .offcanvas__content .offcanvas__contact ul li .offcanvas__contact-icon {
    margin-right: 20px;
}

.offcanvas__wrapper .offcanvas__content .offcanvas__contact ul li .offcanvas__contact-icon i {
    color: var(--theme);
}

.offcanvas__wrapper .offcanvas__content .offcanvas__contact .header-button .theme-btn {
    width: 100%;
    padding: 16px 40px;
}

.offcanvas__wrapper .offcanvas__content .offcanvas__contact .social-icon {
    margin-top: 30px;
    gap: 10px;
}

.offcanvas__wrapper .offcanvas__content .offcanvas__contact .social-icon a {
    width: 45px;
    height: 45px;
    line-height: 45px;
    text-align: center;
    font-size: 16px;
    display: block;
    background: transparent;
    color: var(--header);
    border-radius: 50%;
    transition: all 0.4s ease-in-out;
    text-align: center;
    border: 1px solid var(--border);
}

.offcanvas__wrapper .offcanvas__content .offcanvas__contact .social-icon a:hover {
    background-color: var(--theme);
    color: var(--white);
}

.offcanvas__overlay {
    position: fixed;
    height: 100%;
    width: 100%;
    background: #151515;
    z-index: 900;
    top: 0;
    opacity: 0;
    visibility: hidden;
    right: 0;
}

.offcanvas__overlay.overlay-open {
    opacity: 0.8;
    visibility: visible;
}

@media (max-width: 450px) {
    .offcanvas__info {
        width: 300px;
    }
}

@media (max-width: 575px) {
    .offcanvas__wrapper {
        padding: 20px;
    }
}

.breadcrumb-wrapper {
    position: relative;
    overflow: hidden;
    background-position: bottom !important;
}

.breadcrumb-wrapper::before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    content: "";
    background-color: rgb(7, 1, 30);
    opacity: 0.6;
}

.breadcrumb-wrapper .layer-shape {
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 1;
}

@media (max-width: 991px) {
    .breadcrumb-wrapper .layer-shape {
        display: none;
    }
}

.breadcrumb-wrapper .layer-shape img {
    width: 100%;
    height: 100%;
}

.breadcrumb-wrapper .breadcrumb-shape {
    position: absolute;
    top: -70px;
    right: 0;
    z-index: 1;
}

.breadcrumb-wrapper .breadcrumb-shape img {
    width: 100%;
    height: 100%;
}

@media (max-width: 991px) {
    .breadcrumb-wrapper .breadcrumb-shape {
        display: none;
    }
}

.breadcrumb-wrapper .page-heading {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    z-index: 99;
}

@media (max-width: 767px) {
    .breadcrumb-wrapper .page-heading {
        flex-wrap: wrap;
        gap: 20px;
        justify-content: center;
    }
}

.breadcrumb-wrapper .page-heading h1 {
    color: var(--white);
    font-size: 70px;
    position: relative;
    z-index: 9;
    line-height: 1.3;
}

@media (max-width: 991px) {
    .breadcrumb-wrapper .page-heading h1 {
        font-size: 50px;
    }
}

@media (max-width: 575px) {
    .breadcrumb-wrapper .page-heading h1 {
        font-size: 42px;
    }
}

.breadcrumb-wrapper .page-heading .breadcrumb-items {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.breadcrumb-wrapper .page-heading .breadcrumb-items li {
    font-size: 18px;
    color: var(--white);
    font-weight: 500;
    text-transform: capitalize;
}

@media (max-width: 575px) {
    .breadcrumb-wrapper .page-heading .breadcrumb-items li {
        font-size: 16px;
    }
}

.breadcrumb-wrapper .page-heading .breadcrumb-items li a {
    color: var(--theme);
    transition: all 0.4s ease-in-out;
}

.breadcrumb-wrapper .page-heading .breadcrumb-items li a:hover {
    color: var(--theme);
}

.breadcrumb-wrapper .page-heading .breadcrumb-items li i {
    font-size: 18px;
    color: var(--theme);
}

@media (max-width: 575px) {
    .breadcrumb-wrapper .page-heading .breadcrumb-items li i {
        font-size: 16px;
    }
}

.error-items .error-image {
    margin-bottom: 50px;
}

.error-items .error-image img {
    width: 100%;
    height: 100%;
}

.mean-container a.meanmenu-reveal {
    display: none;
}

.mean-container .mean-nav {
    background: none;
    margin-top: 0;
}

.mean-container .mean-bar {
    padding: 0;
    min-height: auto;
    background: none;
}

.mean-container .mean-nav>ul {
    padding: 0;
    margin: 0;
    width: 100%;
    list-style-type: none;
    display: block !important;
}

.mean-container .mean-nav>ul .homemenu {
    position: relative;
}

.mean-container .mean-nav>ul .homemenu .homemenu-thumb {
    position: relative;
    width: 280px;
}

.mean-container .mean-nav>ul .homemenu .homemenu-thumb .demo-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60%;
    gap: 10px;
    display: flex;
    justify-content: center;
    flex-direction: column;
    opacity: 0;
    visibility: hidden;
    transition: all 0.4s ease-in-out;
}

.mean-container .mean-nav>ul .homemenu .homemenu-thumb .demo-button .theme-btn {
    padding: 10px 30px;
    color: var(--white) !important;
    width: initial;
    font-size: 14px;
    text-align: center;
}

.mean-container .mean-nav>ul .homemenu .homemenu-thumb .demo-button .theme-btn:hover {
    color: var(--white) !important;
}

.mean-container .mean-nav>ul .homemenu .homemenu-thumb::before {
    background: linear-gradient(to bottom, rgba(99, 92, 92, 0) 0%, #292930 100%);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    overflow: hidden;
    opacity: 0;
    transition: all 0.3s ease-in-out;
    content: "";
}

.mean-container .mean-nav>ul .homemenu .homemenu-thumb:hover::before {
    visibility: visible;
    opacity: 1;
}

.mean-container .mean-nav>ul .homemenu .homemenu-thumb:hover .demo-button {
    opacity: 1;
    visibility: visible;
    margin-top: 0;
}

.mean-container .mean-nav>ul .homemenu .homemenu-thumb:hover .homemenu-btn {
    opacity: 1;
    visibility: visible;
    bottom: 50%;
    transform: translateY(50%);
}

.mean-container .mean-nav>ul .homemenu .homemenu-thumb img {
    width: 100%;
}

.mean-container .mean-nav>ul .homemenu .homemenu-content .homemenu-title {
    margin-top: 15px;
    display: inline-block;
    font-size: 16px;
}

.mean-container a.meanmenu-reveal {
    display: none !important;
}

.mean-container .mean-nav ul li a {
    width: 100%;
    padding: 10px 0;
    color: var(--header);
    font-size: 16px;
    line-height: 1.5;
    font-weight: 600;
}

.mean-container .mean-nav ul li a:hover {
    color: var(--theme2);
}

.mean-container .mean-nav ul li a:last-child {
    border-bottom: 0;
}

.mean-container .mean-nav ul li a:hover {
    color: var(--theme2);
}

.mean-container .mean-nav ul li a.mean-expand {
    margin-top: 5px;
    padding: 0 !important;
}

.mean-container .mean-nav ul li>a>i {
    display: none;
}

.mean-container .mean-nav ul li>a.mean-expand i {
    display: inline-block;
}

.mean-container .mean-nav>ul>li:first-child>a {
    border-top: 0;
}

.mean-container .mean-nav ul li a.mean-expand.mean-clicked i {
    transform: rotate(45deg);
    -webkit-transform: rotate(45deg);
    -moz-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    -o-transform: rotate(45deg);
    transition: all 0.4s ease-in-out;
}

.mean-container .mean-nav ul li .mega-menu li a {
    height: 200px;
    width: 100%;
    padding: 0;
    border-top: 0;
    margin-bottom: 20px;
}

@media (max-width: 991px) {
    .mean-container .mean-nav ul li.menu-thumb {
        display: none;
    }
}

.hero-1 {
    position: relative;
}

.hero-1 .array-button {
    position: absolute;
    top: 50%;
    right: 55px;
    display: grid;
    z-index: 9;
    gap: 20px;
    transform: translate(-50%, -50%);
}

@media (max-width: 1199px) {
    .hero-1 .array-button {
        top: 70%;
    }
}

@media (max-width: 767px) {
    .hero-1 .array-button {
        display: none;
    }
}

.hero-1 .swiper-slide-active .hero-image {
    transform: scale(1.12);
}

.hero-1 .hero-image {
    overflow: hidden;
    position: absolute !important;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    opacity: 1;
    z-index: -2;
    background-size: cover;
    transform: scale(1);
    transition: all 8s ease-out 0s;
}

.hero-1 .hero-image::before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    content: "";
    background-color: rgb(2, 0, 12);
    opacity: 0.749;
}

.hero-1 .shape-image {
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 1;
}

@media (max-width: 1399px) {
    .hero-1 .shape-image {
        display: none;
    }
}

.hero-1 .hero-content {
    position: relative;
    padding-top: 350px;
    padding-bottom: 330px;
}

@media (max-width: 1199px) {
    .hero-1 .hero-content {
        padding-top: 280px;
        padding-bottom: 295px;
    }
}

@media (max-width: 991px) {
    .hero-1 .hero-content {
        padding-top: 200px;
        padding-bottom: 205px;
    }
}

@media (max-width: 767px) {
    .hero-1 .hero-content {
        padding-top: 170px;
        padding-bottom: 185px;
    }
}

@media (max-width: 575px) {
    .hero-1 .hero-content {
        padding-top: 145px;
        padding-bottom: 165px;
    }
}

@media (max-width: 1399px) {
    .hero-1 .hero-content h1 {
        font-size: 80px;
    }
}

@media (max-width: 1199px) {
    .hero-1 .hero-content h1 {
        font-size: 72px;
    }
}

@media (max-width: 991px) {
    .hero-1 .hero-content h1 {
        font-size: 62px;
    }
}

@media (max-width: 767px) {
    .hero-1 .hero-content h1 {
        font-size: 48px;
    }
}

@media (max-width: 575px) {
    .hero-1 .hero-content h1 {
        font-size: 36px;
        line-height: 1.3;
    }
}

.hero-1 .hero-content .hero-button {
    margin-top: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 30px;
}

@media (max-width: 575px) {
    .hero-1 .hero-content .hero-button {
        flex-wrap: wrap;
        margin-top: 20px;
    }
}

.hero-1 .hero-content .hero-button .video-play-btn .play-video {
    border-radius: 50%;
    width: 60px;
    height: 60px;
    background-color: transparent;
    color: var(--theme2);
    line-height: 60px;
    text-align: center;
    display: inline-block;
    margin: 0px auto;
    font-size: 18px;
    border: 1px solid var(--white);
}

.hero-1 .hero-content .hero-button .video-play-btn span {
    color: var(--white);
}

.hero-2 {
    position: relative;
}

.hero-2 .array-button {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 9;
    display: grid;
    left: 50px;
    gap: 15px;
}

.hero-2 .array-button .array-prev {
    width: 56px;
    height: 56px;
    line-height: 56px;
    text-align: center;
    background-color: var(--theme);
    color: var(--white);
    border-radius: 50%;
    transition: all 0.4s ease-in-out;
}

.hero-2 .array-button .array-prev:hover {
    background-color: var(--white);
    color: var(--theme);
}

.hero-2 .array-button .array-next {
    width: 56px;
    height: 56px;
    line-height: 56px;
    text-align: center;
    background-color: transparent;
    color: var(--white);
    border-radius: 50%;
    border: 1px solid var(--white);
    transition: all 0.4s ease-in-out;
}

.hero-2 .array-button .array-next:hover {
    background-color: var(--white);
    color: var(--theme);
}

@media (max-width: 1600px) {
    .hero-2 .array-button {
        left: initial;
        right: 30px;
    }
}

@media (max-width: 767px) {
    .hero-2 .array-button {
        display: none;
    }
}

.hero-2 .bottom-shape {
    position: absolute;
    bottom: -180px;
    left: 0;
    right: 0;
    z-index: 2;
}

@media (max-width: 1600px) {
    .hero-2 .bottom-shape {
        bottom: -50px;
    }
}

@media (max-width: 1399px) {
    .hero-2 .bottom-shape {
        display: none;
    }
}

.hero-2 .bottom-shape img {
    width: 100%;
    height: 100%;
}

.hero-2 .swiper-slide-active .slider-image {
    transform: scale(1.12);
}

.hero-2 .slider-image {
    overflow: hidden;
    position: absolute !important;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    opacity: 1;
    z-index: -2;
    background-size: cover;
    transform: scale(1);
    transition: all 8s ease-out 0s;
}

.hero-2 .slider-image::before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    content: "";
    background-color: var(--black);
    opacity: 0.75;
}

.hero-2 .slider-image .mask-shape {
    position: absolute;
    top: 0;
    left: 0;
}

@media (max-width: 1199px) {
    .hero-2 .slider-image .mask-shape {
        display: none;
    }
}

.hero-2 .slider-image .border-shape {
    position: absolute;
    top: 12%;
    right: 0;
}

@media (max-width: 1199px) {
    .hero-2 .slider-image .border-shape {
        display: none;
    }
}

.hero-2 .slider-image .circle-shape {
    position: absolute;
    right: 10%;
    top: 10%;
}

.hero-2 .hero-content {
    position: relative;
    z-index: 9;
    padding: 165px 0;
}

@media (max-width: 1399px) {
    .hero-2 .hero-content {
        padding: 140px 0;
    }
}

@media (max-width: 1199px) {
    .hero-2 .hero-content {
        padding: 120px 0;
    }
}

@media (max-width: 991px) {
    .hero-2 .hero-content {
        padding: 110px 0;
    }
}

@media (max-width: 767px) {
    .hero-2 .hero-content {
        padding: 100px 0;
    }
}

@media (max-width: 575px) {
    .hero-2 .hero-content {
        text-align: center;
        margin: 0 auto;
        padding: 80px 0;
    }
}

.hero-2 .hero-content h5 {
    color: var(--white);
    margin-bottom: 20px;
    position: relative;
    padding-left: 45px;
    display: inline-block;
    font-weight: 500;
}

.hero-2 .hero-content h5::before {
    position: absolute;
    top: 10px;
    left: 0;
    content: "";
    width: 30px;
    height: 2px;
    background-color: var(--white);
}

.hero-2 .hero-content h1 {
    color: var(--white);
    margin-bottom: 20px;
}

@media (max-width: 1399px) {
    .hero-2 .hero-content h1 {
        font-size: 70px;
    }
}

@media (max-width: 1199px) {
    .hero-2 .hero-content h1 {
        font-size: 62px;
    }
}

@media (max-width: 991px) {
    .hero-2 .hero-content h1 {
        font-size: 52px;
    }
}

@media (max-width: 767px) {
    .hero-2 .hero-content h1 {
        font-size: 42px;
    }
}

@media (max-width: 575px) {
    .hero-2 .hero-content h1 {
        font-size: 34px;
    }
}

.hero-2 .hero-content p {
    color: var(--white);
}

.hero-2 .hero-content .hero-button {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-top: 50px;
}

@media (max-width: 767px) {
    .hero-2 .hero-content .hero-button {
        margin-top: 30px;
    }
}

@media (max-width: 575px) {
    .hero-2 .hero-content .hero-button {
        flex-wrap: wrap;
        margin-top: 20px;
        justify-content: center;
    }
}

.hero-3 {
    position: relative;
    padding-top: 340px;
    padding-bottom: 215px;
}

@media (max-width: 1199px) {
    .hero-3 {
        padding-top: 280px;
        padding-bottom: 180px;
    }
}

@media (max-width: 991px) {
    .hero-3 {
        padding-top: 250px;
        padding-bottom: 190px;
    }
}

@media (max-width: 575px) {
    .hero-3 {
        padding-top: 180px;
        padding-bottom: 120px;
    }
}

.hero-3::before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    content: "";
    background-color: rgb(0, 5, 29);
    opacity: 0.8;
}

.hero-3 .hero-content {
    position: relative;
    z-index: 9999;
}

.hero-3 .hero-content h5 {
    font-size: 18px;
    color: var(--white);
    font-weight: normal;
    margin-bottom: 10px;
    padding: 8px 25px;
    border-radius: 30px;
    display: inline-block;
    text-transform: uppercase;
    position: relative;
    z-index: 1;
    overflow: hidden;
}

.hero-3 .hero-content h5::before {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    content: "";
    background: var(--white);
    opacity: 0.2;
    z-index: -1;
    filter: blur(1px);
}

.hero-3 .hero-content h1 {
    font-size: 110px;
    color: var(--white);
    margin-bottom: 30px;
}

@media (max-width: 1199px) {
    .hero-3 .hero-content h1 {
        font-size: 90px;
    }
}

@media (max-width: 991px) {
    .hero-3 .hero-content h1 {
        font-size: 80px;
    }
}

@media (max-width: 767px) {
    .hero-3 .hero-content h1 {
        font-size: 66px;
    }
}

@media (max-width: 575px) {
    .hero-3 .hero-content h1 {
        font-size: 48px;
        margin-bottom: 20px;
    }
}

.hero-3 .hero-content p {
    color: var(--white);
}

.hero-3 .hero-content .hero-button {
    margin-top: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 30px;
}

.hero-3 .hero-content .hero-button .video-play-btn .play-video {
    border-radius: 50%;
    width: 60px;
    height: 60px;
    background-color: transparent;
    color: var(--theme);
    line-height: 60px;
    text-align: center;
    display: inline-block;
    margin: 0px auto;
    font-size: 18px;
    border: 1px solid var(--white);
}

.hero-3 .hero-content .hero-button .video-play-btn span {
    color: var(--white);
}

@media (max-width: 575px) {
    .hero-3 .hero-content .hero-button {
        flex-wrap: wrap;
    }
}

.hero-section-3 {
    position: relative;
    overflow: hidden;
}

.hero-section-3 .array-button {
    position: initial;
}

@media (max-width: 1199px) {
    .hero-section-3 .array-button {
        display: none;
    }
}

.hero-section-3 .array-button .array-prev {
    width: 300px;
    height: 300px;
    line-height: 300px;
    position: absolute;
    top: 50%;
    left: -1%;
    padding-left: 160px;
    z-index: 9;
    transform: translate(-50%, -50%);
    background-color: #161b2f;
    color: var(--white);
}

.hero-section-3 .array-button .array-next {
    width: 300px;
    height: 300px;
    line-height: 300px;
    position: absolute;
    top: 50%;
    right: -16%;
    padding-right: 160px;
    z-index: 9;
    transform: translate(-50%, -50%);
    background-color: var(--black);
    color: var(--theme);
}

@media (max-width: 1399px) {
    .hero-section-3 .array-button .array-next {
        right: -22%;
    }
}

@media (max-width: 1199px) {
    .hero-section-3 .line-area {
        display: none;
    }
}

.hero-section-3 .line-area span {
    position: absolute;
    content: "";
    width: 1px;
    height: 100%;
    top: 108px;
    left: 26%;
    background-color: #2f3245;
    z-index: 3;
}

.hero-section-3 .line-area span:nth-child(2) {
    left: 50%;
}

.hero-section-3 .line-area span:nth-child(3) {
    left: 80%;
}

.hero-4 {
    position: relative;
}

.hero-4 .array-button {
    position: absolute;
    top: 50%;
    display: grid;
    z-index: 9;
    gap: 20px;
    right: 15%;
    transform: translate(-50%, -50%);
}

@media (max-width: 1399px) {
    .hero-4 .array-button {
        display: none;
    }
}

.hero-4 .swiper-slide-active .hero-image {
    transform: scale(1.12);
}

.hero-4 .hero-image {
    overflow: hidden;
    position: absolute !important;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    opacity: 1;
    z-index: 2;
    background-size: cover;
    transform: scale(1);
    transition: all 8s ease-out 0s;
}

.hero-4 .hero-image::before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    content: "";
    background-color: rgb(7, 1, 30);
    opacity: 0.8;
    z-index: -1;
}

.hero-4 .hero-content {
    position: relative;
    z-index: 9;
    padding: 200px 0;
}

@media (max-width: 1199px) {
    .hero-4 .hero-content {
        padding: 180px 0;
    }
}

@media (max-width: 991px) {
    .hero-4 .hero-content {
        padding: 150px 0;
    }
}

@media (max-width: 767px) {
    .hero-4 .hero-content {
        text-align: center;
        margin: 0 auto;
        padding: 130px 0;
    }
}

@media (max-width: 575px) {
    .hero-4 .hero-content {
        padding: 100px 0;
    }
}

.hero-4 .hero-content .video-play-btn .play-video {
    width: 60px;
    height: 60px;
    line-height: 60px;
    text-align: center;
    background-color: var(--theme);
    color: var(--white);
    border-radius: 50%;
    display: inline-block;
}

.hero-4 .hero-content .video-play-btn span {
    color: var(--white);
}

.hero-4 .hero-content h1 {
    margin-top: 30px;
}

@media (max-width: 1199px) {
    .hero-4 .hero-content h1 {
        font-size: 86px;
    }
}

@media (max-width: 991px) {
    .hero-4 .hero-content h1 {
        font-size: 76px;
    }
}

@media (max-width: 767px) {
    .hero-4 .hero-content h1 {
        font-size: 62px;
    }
}

@media (max-width: 575px) {
    .hero-4 .hero-content h1 {
        font-size: 42px;
    }
}

.hero-4 .hero-content .hero-button {
    margin-top: 50px;
    display: flex;
    align-items: center;
    gap: 30px;
    z-index: 9;
    position: relative;
}

.hero-4 .hero-content .hero-button .contact-icon i {
    color: var(--theme);
    font-size: 18px;
    margin-right: 10px;
}

.hero-4 .hero-content .hero-button .contact-icon a {
    font-size: 18px;
    color: var(--white);
}

@media (max-width: 767px) {
    .hero-4 .hero-content .hero-button {
        justify-content: center;
    }
}

@media (max-width: 575px) {
    .hero-4 .hero-content .hero-button {
        flex-wrap: wrap;
        margin-top: 30px;
    }
}

.hero-4 .shape-image {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 2;
    bottom: 0;
}

.hero-4 .shape-image img {
    height: 100%;
}

.hero-4 .shape-image-2 {
    position: absolute;
    top: -20%;
    right: 0;
    z-index: 9;
}

@media (max-width: 991px) {
    .hero-4 .shape-image-2 {
        display: none;
    }
}

.hero-4 .box-shape {
    position: absolute;
    right: 0;
    bottom: -5px;
    z-index: 9;
}

@media (max-width: 991px) {
    .hero-4 .box-shape {
        display: none;
    }
}

@media (max-width: 767px) {
    .logo img {
        max-width: 90%;
    }
}

@media (max-width: 991px) {
    .growth-section {
        padding-top: 60px;
    }
}

.growth-wrapper .growth-content .circle-progress-bar-wrapper {
    margin-top: 50px;
}

@media (max-width: 575px) {
    .growth-wrapper .growth-content .circle-progress-bar-wrapper {
        margin-top: 20px;
    }
}

.growth-wrapper .growth-content .circle-progress-bar-wrapper .single-circle-bar {
    display: flex;
    align-items: center;
    gap: 20px;
}

.growth-wrapper .growth-content .circle-progress-bar-wrapper .single-circle-bar span {
    font-size: 16px;
    line-height: 1;
    font-weight: 700;
}

.growth-wrapper .growth-content .circle-progress-bar-wrapper .single-circle-bar .content h5 {
    display: block;
    margin-bottom: 0;
}

@media (max-width: 575px) {
    .growth-wrapper .growth-content .circle-progress-bar-wrapper .single-circle-bar .content br {
        display: block;
    }
}

.growth-wrapper .growth-content .circle-progress-bar-wrapper .single-circle-bar .circle-bar {
    position: relative;
    height: 120px;
    width: 120px;
}

.growth-wrapper .growth-content .circle-progress-bar-wrapper .single-circle-bar .circle-bar div {
    position: absolute;
    height: 100px;
    width: 100px;
    border-radius: 50%;
}

.growth-wrapper .growth-content .circle-progress-bar-wrapper .single-circle-bar .circle-bar div span {
    position: absolute;
    font-size: 30px;
    line-height: 92px;
    height: 90px;
    width: 90px;
    left: 5px;
    top: 5px;
    font-weight: 700;
    text-align: center;
    border-radius: 50%;
    background-color: var(--white);
    color: var(--header);
}

.growth-wrapper .growth-content .circle-progress-bar-wrapper .single-circle-bar .circle-bar .background {
    background-color: var(--bg) !important;
}

.growth-wrapper .growth-content .circle-progress-bar-wrapper .single-circle-bar .circle-bar .rotate {
    clip: rect(0 50px 100px 0);
    background-color: var(--theme) !important;
}

.growth-wrapper .growth-content .circle-progress-bar-wrapper .single-circle-bar .circle-bar .left {
    clip: rect(0 50px 100px 0);
    opacity: 1;
    background-color: var(--bg) !important;
}

.growth-wrapper .growth-content .circle-progress-bar-wrapper .single-circle-bar .circle-bar .right {
    clip: rect(0 50px 100px 0);
    transform: rotate(180deg);
    opacity: 0;
    background-color: var(--theme) !important;
}

.growth-wrapper .growth-image {
    width: 1027px;
    height: 586px;
    margin-left: 4%;
    position: relative;
}

@media (max-width: 1199px) {
    .growth-wrapper .growth-image {
        margin-left: 10%;
    }
}

@media (max-width: 991px) {
    .growth-wrapper .growth-image {
        margin-left: 0;
        max-width: 1027px;
        width: initial;
    }
}

@media (max-width: 575px) {
    .growth-wrapper .growth-image {
        height: 450px;
    }
}

.growth-wrapper .growth-image .shape-image {
    position: absolute;
    bottom: 0;
    left: -40px;
}

.video-section {
    position: relative;
}

.video-section::before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    content: "";
    background-color: rgb(13, 6, 44);
    opacity: 0.8;
}

.website-checking {
    position: relative;
    background-attachment: fixed;
}

.website-checking::before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    content: "";
    background-image: var(---color-gradient-2);
    opacity: 0.8;
}

.website-checking-wrapper {
    position: relative;
    z-index: 9;
}

.website-checking-wrapper .checking-area {
    display: flex;
    align-items: center;
    margin-top: 30px;
    justify-content: center;
    gap: 30px;
}

@media (max-width: 991px) {
    .website-checking-wrapper .checking-area {
        flex-wrap: wrap;
    }
}

.website-checking-wrapper .checking-area .check-items {
    justify-content: center;
}

.website-checking-wrapper .checking-area .check-items .check-input:not(:last-child) {
    border-right: 1px solid var(--border);
}

.website-checking-wrapper .checking-area .check-items .check-input input {
    width: 100%;
    border: none;
    background-color: var(--white);
    padding: 18px 40px;
    width: 415px;
    color: var(--text);
}

@media (max-width: 1199px) {
    .website-checking-wrapper .checking-area .check-items .check-input input {
        width: initial;
        max-width: 415px;
    }
}

@media (max-width: 575px) {
    .website-checking-wrapper .checking-area .check-items .check-input input {
        padding: 16px 20px;
    }
}

@media screen and (max-width: 430px) {
    .website-checking-wrapper .checking-area .check-items .check-input input {
        width: 170px;
    }
}

.website-checking-wrapper h6 {
    font-size: 18px;
    color: var(--white);
    text-align: center;
    font-weight: normal;
    margin-top: 50px;
}

.website-checking-wrapper h6 a {
    color: var(--header);
}

.website-checking-wrapper .counter-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: -18px;
}

@media (max-width: 1199px) {
    .website-checking-wrapper .counter-wrapper {
        flex-wrap: wrap;
        gap: 30px;
    }
}

.website-checking-wrapper .counter-wrapper .counter-items h2 {
    font-size: 60px;
    color: var(--white);
}

@media (max-width: 575px) {
    .website-checking-wrapper .counter-wrapper .counter-items h2 {
        font-size: 48px;
    }
}

.website-checking-wrapper .counter-wrapper .counter-items p {
    font-size: 18px;
    color: var(--white);
}

.website-checking-wrapper .icon-area {
    padding-top: 120px;
    margin-bottom: -200px;
}

@media (max-width: 1199px) {
    .website-checking-wrapper .icon-area {
        margin-bottom: -230px;
        padding-top: 100px;
    }
}

@media (max-width: 991px) {
    .website-checking-wrapper .icon-area {
        margin-bottom: -160px;
        padding-top: 80px;
    }
}

.website-checking-wrapper .icon-area .icon-items {
    padding: 30px 40px;
    background-color: var(--white);
    display: flex;
    gap: 30px;
    box-shadow: var(---box-shadow);
}

@media (max-width: 575px) {
    .website-checking-wrapper .icon-area .icon-items {
        gap: 15px;
    }
}

.website-checking-wrapper .icon-area .icon-items .icon {
    font-size: 42px;
    color: var(--theme);
}

.website-checking-wrapper .icon-area .icon-items .content h5 {
    margin-bottom: 10px;
}

.website-checking-wrapper .icon-area .icon-items.style-2 {
    background-color: var(--header);
}

.website-checking-wrapper .icon-area .icon-items.style-2 .content h5 {
    color: var(--white);
}

.website-checking-wrapper .icon-area .icon-items.style-2 .content p {
    color: var(--border);
}

.choose-us-section {
    position: relative;
}

.choose-us-section .shape-image {
    position: absolute;
    right: 0;
    top: 10%;
}

@media (max-width: 1199px) {
    .choose-us-section .shape-image {
        display: none;
    }
}

@media (max-width: 1199px) {
    .choose-us-section .line-area {
        display: none;
    }
}

.choose-us-section .line-area span {
    position: absolute;
    content: "";
    width: 1px;
    height: 100%;
    top: 0;
    left: 26%;
    background-color: #efefef;
    z-index: -1;
}

.choose-us-section .line-area span:nth-child(2) {
    left: 50%;
}

.choose-us-section .line-area span:nth-child(3) {
    left: 80%;
}

.choose-us-section .left-shape {
    position: absolute;
    top: 25%;
    left: 3%;
}

@media (max-width: 991px) {
    .choose-wrapper {
        margin-bottom: 80px;
    }
}

@media (max-width: 575px) {
    .choose-wrapper {
        margin-bottom: -80px;
    }
}

.choose-wrapper .choose-content ul {
    margin-top: 30px;
}

.choose-wrapper .choose-content ul li {
    font-weight: 500;
    color: var(--header);
}

.choose-wrapper .choose-content ul li:not(:last-child) {
    margin-bottom: 10px;
}

.choose-wrapper .choose-content ul li i {
    color: var(--theme);
    margin-right: 5px;
}

.choose-wrapper .choose-content .icon-area {
    margin-top: 40px;
}

.choose-wrapper .choose-content .icon-area .icon-items {
    display: flex;
    align-items: center;
    gap: 30px;
}

@media (max-width: 575px) {
    .choose-wrapper .choose-content .icon-area .icon-items {
        gap: 15px;
    }
}

.choose-wrapper .choose-content .icon-area .icon-items:not(:last-child) {
    margin-bottom: 30px;
}

.choose-wrapper .choose-content .icon-area .icon-items .icon {
    width: 90px;
    height: 90px;
    border-radius: 50%;
    font-size: 42px;
    color: var(--white);
    background-color: var(--header);
    text-align: center;
    padding: 25px;
}

.choose-wrapper .choose-content .icon-area .icon-items .content {
    border-left: 2px solid var(--header);
    padding-left: 30px;
}

.choose-wrapper .choose-content .icon-area .icon-items .content h5 {
    margin-bottom: 5px;
}

.choose-wrapper .choose-content .icon-area .icon-items.style-2 .icon {
    background-color: var(--theme);
}

.choose-wrapper .choose-content .icon-area .icon-items.style-2 .content {
    border-left: 2px solid var(--border);
}

.choose-wrapper .choose-image-items {
    position: relative;
}

.choose-wrapper .choose-image-items .choose-image {
    height: 390px;
    max-width: 370px;
    position: relative;
    z-index: 9;
}

@media (max-width: 575px) {
    .choose-wrapper .choose-image-items .choose-image {
        max-width: 490px;
    }
}

.choose-wrapper .choose-image-items .choose-image-2 {
    max-width: 370px;
    position: absolute;
    bottom: -44%;
    right: 0;
}

@media (max-width: 991px) {
    .choose-wrapper .choose-image-items .choose-image-2 {
        bottom: -21%;
    }
}

.choose-wrapper .choose-image-items .choose-image-2 img {
    width: 100%;
}

@media (max-width: 575px) {
    .choose-wrapper .choose-image-items .choose-image-2 {
        width: 210px;
        height: 210px;
        bottom: 0;
    }
}

.choose-wrapper .choose-image-items .circle-shape {
    position: absolute;
    left: 5%;
    bottom: -40%;
    z-index: -1;
}

.choose-wrapper .choose-image-items .circle-shape .text-circle {
    animation: cir36 10s linear infinite;
}

@media (max-width: 1199px) {
    .choose-wrapper .choose-image-items .circle-shape {
        display: none;
    }
}

.choose-wrapper.style-2 .choose-image-items {
    position: relative;
}

.choose-wrapper.style-2 .choose-image-items .choose-image {
    height: 480px;
    max-width: 370px;
    z-index: initial;
    position: relative;
}

@media (max-width: 1199px) {
    .choose-wrapper.style-2 .choose-image-items .choose-image {
        height: 400px;
    }
}

@media (max-width: 991px) {
    .choose-wrapper.style-2 .choose-image-items .choose-image {
        height: 470px;
    }
}

@media (max-width: 575px) {
    .choose-wrapper.style-2 .choose-image-items .choose-image {
        max-width: 540px;
        height: 430px;
    }
}

.choose-wrapper.style-2 .choose-image-items .choose-image .box-shape {
    position: absolute;
    right: -33%;
    top: 24%;
}

@media (max-width: 1199px) {
    .choose-wrapper.style-2 .choose-image-items .choose-image .box-shape {
        display: none;
    }
}

.choose-wrapper.style-2 .choose-image-items .choose-image-2 {
    max-width: 300px;
    bottom: -41%;
    right: 10%;
}

@media (max-width: 991px) {
    .choose-wrapper.style-2 .choose-image-items .choose-image-2 {
        bottom: 10%;
        right: 10%;
    }
}

@media (max-width: 767px) {
    .choose-wrapper.style-2 .choose-image-items .choose-image-2 {
        max-width: 200px;
        bottom: -5%;
    }
}

.choose-wrapper.style-2 .choose-image-items .icon-box {
    width: 170px;
    height: 170px;
    line-height: 180px;
    border-radius: 50%;
    text-align: center;
    color: var(--theme);
    font-size: 28px;
    background-color: var(--white);
    box-shadow: var(---box-shadow);
    position: absolute;
    bottom: -25%;
    left: 40%;
    transform: translateX(-50%);
}

.choose-wrapper.style-2 .choose-image-items .icon-box .circle-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.choose-wrapper.style-2 .choose-image-items .icon-box .text-circle {
    animation: cir36 10s linear infinite;
}

.choose-wrapper.style-2 .choose-content .icon-area.style-2 .icon-items {
    background-color: var(--white);
    box-shadow: var(---box-shadow);
    padding: 30px;
    align-items: initial;
    transition: all 0.4s ease-in-out;
    border-bottom: 3px solid var(--white);
}

@media (max-width: 585px) {
    .choose-wrapper.style-2 .choose-content .icon-area.style-2 .icon-items {
        padding: 20px 15px;
    }
}

.choose-wrapper.style-2 .choose-content .icon-area.style-2 .icon-items:not(:last-child) {
    margin-bottom: 20px;
}

.choose-wrapper.style-2 .choose-content .icon-area.style-2 .icon-items .icon {
    width: initial;
    height: initial;
    border-radius: initial;
    font-size: 66px;
    color: var(--theme);
    background-color: initial;
    text-align: initial;
    padding: 0;
    transition: all 500ms ease;
}

@media (max-width: 575px) {
    .choose-wrapper.style-2 .choose-content .icon-area.style-2 .icon-items .icon {
        font-size: 48px;
    }
}

.choose-wrapper.style-2 .choose-content .icon-area.style-2 .icon-items .content {
    padding: 0;
    border: none;
}

.choose-wrapper.style-2 .choose-content .icon-area.style-2 .icon-items:hover {
    border-bottom: 3px solid var(--theme);
}

.choose-wrapper.style-2 .choose-content .icon-area.style-2 .icon-items:hover .icon {
    transform: scaleX(-1);
}

.choose-wrapper.style-2 .choose-content .icon-area.style-2 .icon-items.active {
    border-bottom: 3px solid var(--theme);
}

.choose-wrapper.style-2 .choose-content .about-author {
    display: flex;
    align-items: center;
    gap: 60px;
    margin-top: 50px;
}

@media (max-width: 1199px) {
    .choose-wrapper.style-2 .choose-content .about-author {
        gap: 30px;
    }
}

@media (max-width: 575px) {
    .choose-wrapper.style-2 .choose-content .about-author {
        gap: 30px;
        flex-wrap: wrap;
    }
}

.choose-wrapper.style-2 .choose-content .about-author .author-image {
    display: flex;
    align-items: center;
    gap: 20px;
}

.transforming-wrapper {
    margin-bottom: -100px;
    position: relative;
    z-index: 9;
}

.transforming-wrapper .transforming-area {
    background-color: rgb(25, 24, 32);
    padding: 60px 40px;
    border: 20px solid var(--white);
}

@media (max-width: 991px) {
    .transforming-wrapper .transforming-area {
        border: none;
    }
}

@media (max-width: 575px) {
    .transforming-wrapper .transforming-area br {
        display: initial;
    }
}

.transforming-wrapper .transforming-area .transforming-items {
    display: flex;
    align-items: center;
    gap: 25px;
}

.transforming-wrapper .transforming-area .transforming-items:not(:last-child) {
    margin-bottom: 40px;
}

.transforming-wrapper .transforming-area .transforming-items .icon {
    width: 50px;
    height: 50px;
    line-height: 55px;
    border-radius: 50%;
    text-align: center;
    color: var(--white);
    background-image: var(--color-gradient-1);
    font-size: 18px;
}

.transforming-wrapper .transforming-area .transforming-items .content h5 {
    color: var(--white);
}

.transforming-wrapper .transforming-content {
    margin-left: 50px;
}

@media (max-width: 1199px) {
    .transforming-wrapper .transforming-content {
        margin-left: 10px;
        margin-bottom: 50px;
    }
}

@media (max-width: 991px) {
    .transforming-wrapper .transforming-content {
        margin-left: 0;
        margin-bottom: 170px;
    }
}

.transforming-ideas {
    position: relative;
}

@media (max-width: 1199px) {
    .transforming-ideas .line-area {
        display: none;
    }
}

.transforming-ideas .line-area span {
    position: absolute;
    content: "";
    width: 1px;
    height: 100%;
    top: 0;
    left: 26%;
    background-color: #efefef;
    z-index: -1;
}

.transforming-ideas .line-area span:nth-child(2) {
    left: 50%;
}

.transforming-ideas .line-area span:nth-child(3) {
    left: 80%;
}

.counter-section {
    position: relative;
}

.counter-section::before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    content: "";
    background-color: var(--bg);
    top: 50%;
}

.counter-wrapper-area .counter-items {
    text-align: center;
    background-color: var(--white);
    box-shadow: var(---box-shadow);
    padding: 45px 30px;
    position: relative;
    z-index: 9;
}

.counter-wrapper-area .counter-items::before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    content: "";
    background-image: var(--color-gradient-1);
    transform: scale(1, 0);
    transition: transform 500ms ease;
    transform-origin: bottom center;
    z-index: -1;
}

.counter-wrapper-area .counter-items::after {
    position: absolute;
    top: -50%;
    right: 0;
    content: "";
    width: 95px;
    height: 92px;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url(/website/img/shape-3.webp);
    opacity: 0;
    visibility: hidden;
    transition: all 0.4s ease-in-out;
    z-index: 1;
}

.counter-wrapper-area .counter-items h2 {
    font-size: 45px;
    margin-bottom: 5px;
    color: var(--theme);
    position: relative;
    z-index: 9;
}

.counter-wrapper-area .counter-items p {
    font-size: 18px;
    color: var(--header);
    font-weight: 500;
    position: relative;
    z-index: 9;
}

.counter-wrapper-area .counter-items:hover::before {
    transform: scale(1, 1);
    transform-origin: top center;
}

.counter-wrapper-area .counter-items:hover::after {
    opacity: 1;
    visibility: visible;
    top: 0;
}

.counter-wrapper-area .counter-items:hover h2,
.counter-wrapper-area .counter-items:hover p {
    color: var(--white);
}

.counter-wrapper-area .counter-items.active::before {
    transform: scale(1, 1);
    transform-origin: top center;
}

.counter-wrapper-area .counter-items.active::after {
    opacity: 1;
    visibility: visible;
    top: 0;
}

.counter-wrapper-area .counter-items.active h2,
.counter-wrapper-area .counter-items.active p {
    color: var(--white);
}

.feature-wrapper .feature-content .feature-button {
    margin-top: 40px;
}

.feature-wrapper .feature-image img {
    width: 100%;
    height: 100%;
}

.clear-post-wrapper .post-content {
    padding: 50px;
    background-color: var(--bg);
    position: relative;
    z-index: 9;
}

.clear-post-wrapper .post-content::before {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    content: "";
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url(/website/img/post-shape.webp);
    z-index: -1;
}

@media (max-width: 575px) {
    .clear-post-wrapper .post-content {
        padding: 40px 30px;
    }
}

.clear-post-wrapper .post-content h3 {
    font-size: 36px;
    margin-bottom: 20px;
}

@media (max-width: 575px) {
    .clear-post-wrapper .post-content h3 {
        font-size: 28px;
    }
}

.clear-post-wrapper .post-content p {
    margin-bottom: 30px;
}

.clear-post-wrapper .post-image {
    height: 560px;
    width: 400px;
    margin-left: -55%;
    margin-top: -58%;
}

@media (max-width: 1199px) {
    .clear-post-wrapper .post-image {
        width: initial;
        height: 455px;
        max-width: 470px;
        margin-left: 0;
        margin-top: 0;
    }
}

@media (max-width: 991px) {
    .clear-post-wrapper .post-image {
        max-width: 700px;
        margin-top: 30px;
        margin-left: 0;
    }
}

@media (max-width: 767px) {
    .clear-post-wrapper .post-image {
        height: 450px;
        max-width: 540px;
    }
}

.clear-post-wrapper .progress-content {
    position: relative;
    z-index: 9;
}

.clear-post-wrapper .progress-content h2 {
    margin-bottom: 20px;
}

.clear-post-wrapper .progress-content .progress-wrap {
    margin-top: 30px;
}

.clear-post-wrapper .progress-content .progress-wrap .pro-items:not(:last-child) {
    margin-bottom: 20px;
}

.clear-post-wrapper .progress-content .progress-wrap .pro-items .pro-head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
}

.clear-post-wrapper .progress-content .progress-wrap .pro-items .pro-head .point {
    font-size: 16px;
    color: var(--text);
}

.clear-post-wrapper .progress-content .progress-wrap .pro-items .progress {
    background: #ebebeb;
    justify-content: flex-start;
    border-radius: 0;
    align-items: center;
    position: relative;
    display: flex;
    height: 6px;
    width: 100%;
}

.clear-post-wrapper .progress-content .progress-wrap .pro-items .progress-value {
    animation: load 3s normal forwards;
    box-shadow: 0 10px 40px -10px var(--white);
    border-radius: 0;
    background: var(--theme);
    height: 6px;
    width: 0;
}

.clear-post-wrapper .progress-content .progress-wrap .pro-items .style-two {
    animation: load2 3s normal forwards;
}

.clear-post-wrapper .progress-content .progress-wrap .pro-items .style-three {
    animation: load3 3s normal forwards;
}

@keyframes load {
    0% {
        width: 0;
    }
    100% {
        width: 70%;
    }
}

@keyframes load2 {
    0% {
        width: 0;
    }
    100% {
        width: 85%;
    }
}

.single-request-element {
    margin-top: 40px;
}

@media (max-width: 767px) {
    .single-request-element {
        text-align: center;
        margin-top: 30px;
    }
}

.single-request-element .icon {
    font-size: 40px;
}

.single-request-element h6 {
    margin-top: 20px;
    margin-bottom: 0;
    font-size: 17px;
    text-transform: capitalize;
    color: var(--white);
}

.about-wrapper .about-image {
    position: relative;
}

.about-wrapper .about-image img {
    width: 100%;
    height: 100%;
}

.about-wrapper .about-image .video-box {
    position: absolute;
    bottom: 65px;
    right: -200px;
}

@media (max-width: 1199px) {
    .about-wrapper .about-image .video-box {
        width: 480px;
        right: -100px;
    }
}

@media (max-width: 991px) {
    .about-wrapper .about-image .video-box {
        bottom: 0;
        right: 0;
        width: initial;
    }
}

.about-wrapper .about-image .video-box .video-btn {
    background-color: var(--white);
    color: var(--header);
    font-size: 25px;
    height: 70px;
    width: 70px;
    line-height: 75px;
    border-radius: 50%;
    position: absolute;
    text-align: center;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.about-wrapper .about-content {
    margin-left: 40px;
}

@media (max-width: 1399px) {
    .about-wrapper .about-content {
        margin-left: 30px;
    }
}

@media (max-width: 1199px) {
    .about-wrapper .about-content {
        margin-left: 20px;
    }
}

@media (max-width: 991px) {
    .about-wrapper .about-content {
        margin-left: 0;
    }
}

@media (max-width: 1199px) {
    .about-wrapper .about-content br {
        display: none;
    }
}

.about-wrapper .about-content .about-list {
    margin-left: 200px;
    margin-top: 40px;
}

@media (max-width: 1199px) {
    .about-wrapper .about-content .about-list {
        margin-left: 100px;
    }
}

@media (max-width: 991px) {
    .about-wrapper .about-content .about-list {
        margin-left: 0;
    }
}

.about-wrapper .about-content .about-list li {
    font-size: 18px;
    color: var(--header);
    background-color: var(--bg);
    width: 300px;
    padding: 15px 20px;
    transition: all 0.4s ease-in-out;
}

@media (max-width: 575px) {
    .about-wrapper .about-content .about-list li {
        width: 100%;
    }
}

.about-wrapper .about-content .about-list li:not(:last-child) {
    margin-bottom: 15px;
}

.about-wrapper .about-content .about-list li i {
    color: var(--theme);
    margin-right: 5px;
}

.about-wrapper .about-content .about-author {
    display: flex;
    align-items: center;
    gap: 60px;
    margin-top: 150px;
}

@media (max-width: 1199px) {
    .about-wrapper .about-content .about-author {
        gap: 30px;
        margin-top: 80px;
    }
}

@media (max-width: 991px) {
    .about-wrapper .about-content .about-author {
        margin-top: 60px;
    }
}

@media (max-width: 575px) {
    .about-wrapper .about-content .about-author {
        margin-top: 40px;
        flex-wrap: wrap;
    }
}

.about-wrapper .about-content .about-author .author-image {
    display: flex;
    align-items: center;
    gap: 20px;
}

@media (max-width: 991px) {
    .about-wrapper br {
        display: none;
    }
}

.about-wrapper-2 {
    position: relative;
}

.about-wrapper-2 .arrow-shape {
    position: absolute;
    top: -2px;
    left: -10px;
}

@media (max-width: 1600px) {
    .about-wrapper-2 .arrow-shape {
        display: none;
    }
}

.about-wrapper-2 .about-image-area .about-image {
    height: 631px;
    max-width: 458px;
    border-right: 10px solid var(--theme);
    position: relative;
}

@media (max-width: 767px) {
    .about-wrapper-2 .about-image-area .about-image {
        height: 420px;
    }
}

@media (max-width: 575px) {
    .about-wrapper-2 .about-image-area .about-image {
        height: 320px;
    }
}

.about-wrapper-2 .about-image-area .about-image .about-image-2 {
    max-width: 370px;
    height: 498px;
    right: -40%;
    top: 70px;
    position: absolute;
}

@media (max-width: 1399px) {
    .about-wrapper-2 .about-image-area .about-image .about-image-2 {
        right: -20%;
    }
}

@media (max-width: 1199px) {
    .about-wrapper-2 .about-image-area .about-image .about-image-2 {
        right: -40%;
    }
}

@media (max-width: 767px) {
    .about-wrapper-2 .about-image-area .about-image .about-image-2 {
        height: 320px;
        right: -15%;
    }
    .about-wrapper-2 .about-image-area .about-image .about-image-2 img {
        width: 100%;
        height: 100%;
        -o-object-fit: cover;
        object-fit: cover;
    }
}

@media (max-width: 575px) {
    .about-wrapper-2 .about-image-area .about-image .about-image-2 {
        height: 210px;
        width: 210px;
        right: 0;
        top: initial;
        bottom: 0;
    }
}

.about-wrapper-2 .about-content {
    margin-left: 40px;
}

@media (max-width: 1199px) {
    .about-wrapper-2 .about-content {
        margin-left: 0;
    }
}

.about-wrapper-2 .about-content .about-icon-items {
    margin-top: 40px;
    display: flex;
    gap: 25px;
    padding: 25px 35px;
    background-color: var(--white);
    box-shadow: var(---box-shadow);
}

.about-wrapper-2 .about-content .about-icon-items .icon {
    font-size: 42px;
    color: var(--theme);
}

.about-wrapper-2 .about-content .about-icon-items .content h5 {
    margin-bottom: 10px;
}

.about-wrapper-2 .about-content .about-list {
    margin-top: 40px;
    margin-bottom: 40px;
}

.about-wrapper-2 .about-content .about-list li {
    color: var(--header);
    font-weight: 500;
}

.about-wrapper-2 .about-content .about-list li:not(:last-child) {
    margin-bottom: 10px;
}

.about-wrapper-2 .about-content .about-list li i {
    color: var(--theme);
    margin-right: 5px;
}

.about-wrapper-3 .about-image {
    position: relative;
    width: 400px;
    height: 560px;
    margin-left: 130px;
    margin-bottom: 20px;
}

@media (max-width: 1199px) {
    .about-wrapper-3 .about-image {
        margin-left: 50px;
    }
}

@media (max-width: 991px) {
    .about-wrapper-3 .about-image {
        margin-bottom: 0;
        margin-left: 240px;
        width: 400px;
        height: 560px;
    }
}

@media (max-width: 767px) {
    .about-wrapper-3 .about-image {
        margin-left: 130px;
    }
}

@media (max-width: 575px) {
    .about-wrapper-3 .about-image {
        margin-left: 0;
        margin-bottom: 0;
        text-align: center;
        margin: 0 auto;
        width: initial;
        max-width: 400px;
        height: 350px;
    }
}

.about-wrapper-3 .about-image .about-image-2 {
    width: 390px;
    height: 360px;
    position: absolute;
    bottom: -20px;
    left: -57%;
}

@media (max-width: 1399px) {
    .about-wrapper-3 .about-image .about-image-2 {
        left: -35%;
    }
}

@media (max-width: 1199px) {
    .about-wrapper-3 .about-image .about-image-2 {
        width: 330px;
        height: 280px;
        left: -10%;
    }
}

@media (max-width: 991px) {
    .about-wrapper-3 .about-image .about-image-2 {
        width: 390px;
        height: 360px;
        left: -57%;
    }
}

@media (max-width: 767px) {
    .about-wrapper-3 .about-image .about-image-2 {
        bottom: 30px;
        left: -30%;
    }
}

@media (max-width: 575px) {
    .about-wrapper-3 .about-image .about-image-2 {
        width: 210px;
        height: 210px;
        bottom: 0;
        left: 0;
    }
}

@media (max-width: 575px) {
    .about-wrapper-3 .about-content {
        text-align: center;
        margin: 0 auto;
    }
}

.about-wrapper-3 .about-content h5 {
    color: var(--theme);
}

.about-wrapper-3 .about-content p {
    margin-top: 30px;
}

.about-wrapper-3 .about-content .author-items {
    display: flex;
    align-items: center;
    gap: 60px;
    margin-top: 50px;
}

@media (max-width: 575px) {
    .about-wrapper-3 .about-content .author-items {
        margin-top: 30px;
        flex-wrap: wrap;
        gap: 30px;
        justify-content: center;
    }
}

.about-section {
    position: relative;
}

.about-section .shape-image {
    position: absolute;
    top: 0;
    left: 0;
}

.about-section .shape-image-2 {
    position: absolute;
    top: 10%;
    right: 0;
    z-index: -1;
}

@media (max-width: 1199px) {
    .about-section .line-area {
        display: none;
    }
}

.about-section .line-area span {
    position: absolute;
    content: "";
    width: 1px;
    height: 100%;
    top: 0;
    left: 26%;
    background-color: #efefef;
    z-index: -1;
}

.about-section .line-area span:nth-child(2) {
    left: 50%;
}

.about-section .line-area span:nth-child(3) {
    left: 80%;
}

.service-section {
    position: relative;
}

.service-section::before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    content: "";
    background-color: var(--bg);
    top: 40%;
    z-index: -1;
}

.servie-card-image-items {
    padding: 70px 45px;
    background-color: var(--theme);
    position: relative;
    height: 100%;
}

@media (max-width: 575px) {
    .servie-card-image-items {
        padding: 50px 30px;
    }
}

.servie-card-image-items .content p {
    color: var(--white);
    font-weight: 500;
}

.servie-card-image-items .content h3 {
    font-size: 26px;
    color: var(--white);
    margin-top: 20px;
    line-height: 1.3;
}

@media (max-width: 575px) {
    .servie-card-image-items .content h3 {
        margin-top: 15px;
    }
}

.service-card-items {
    padding: 50px 20px;
    background-color: var(--white);
    box-shadow: var(---box-shadow);
    height: 100%;
    position: relative;
    z-index: 9;
}

@media (max-width: 575px) {
    .service-card-items {
        padding: 40px 20px;
    }
}

.service-card-items .icon {
    width: 120px;
    height: 120px;
    line-height: 130px;
    border-radius: 50%;
    background-color: var(--bg);
    text-align: center;
    margin: 0 auto;
    position: relative;
    transition: all 500ms ease;
}

@media (max-width: 575px) {
    .service-card-items .icon {
        width: 90px;
        height: 90px;
        line-height: 100px;
    }
}

.service-card-items .icon::before {
    background-color: var(--theme);
    content: "";
    height: 100%;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    visibility: hidden;
    opacity: 0;
    transition: all 0.3s ease-in-out;
    border-radius: 50%;
}

.service-card-items .icon i {
    font-size: 60px;
    color: var(--theme);
    transition: all 0.4s ease-in-out;
}

@media (max-width: 575px) {
    .service-card-items .icon i {
        font-size: 48px;
    }
}

.service-card-items .content {
    margin-top: 30px;
}

@media (max-width: 575px) {
    .service-card-items .content {
        margin-top: 20px;
    }
}

.service-card-items .content h5 a:hover {
    color: var(--theme);
}

.service-card-items:hover .icon {
    transform: scaleX(-1);
}

.service-card-items:hover .icon::before {
    visibility: visible;
    opacity: 1;
}

.service-card-items:hover .icon i {
    color: var(--white);
    position: relative;
    z-index: 9;
}

.service-card-items.active .icon::before {
    visibility: visible;
    opacity: 1;
}

.service-card-items.active .icon i {
    color: var(--white);
    position: relative;
    z-index: 9;
}

.service-card-items.style-2 {
    position: relative;
    overflow: hidden;
}

.service-card-items.style-2 .icon,
.service-card-items.style-2 .content {
    position: relative;
    z-index: 9;
}

.service-card-items.style-2::before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    content: "";
    background-image: url(/website/img/service/03.webp);
    background-repeat: no-repeat;
    background-size: cover;
    opacity: 0;
    visibility: hidden;
    transition: all 0.4s ease-in-out;
}

.service-card-items.style-2::after {
    position: absolute;
    top: 10%;
    left: 17%;
    background-image: url(/website/img/service/shape-2.webp);
    background-repeat: no-repeat;
    background-size: cover;
    width: 200px;
    height: 94px;
    content: "";
    opacity: 0;
    visibility: hidden;
    transition: all 0.4s ease-in-out;
}

@media (max-width: 1399px) {
    .service-card-items.style-2::after {
        left: 12%;
    }
}

@media (max-width: 1199px) {
    .service-card-items.style-2::after {
        left: 17%;
    }
}

@media (max-width: 767px) {
    .service-card-items.style-2::after {
        display: none;
    }
}

.service-card-items.style-2:hover .icon::before {
    background-color: var(--white);
    background-image: initial;
}

.service-card-items.style-2:hover .icon i {
    color: var(--theme);
}

.service-card-items.style-2:hover::before {
    opacity: 1;
    visibility: visible;
}

.service-card-items.style-2:hover::after {
    opacity: 1;
    visibility: visible;
    top: 37%;
}

.service-card-items.style-2:hover .content h5 a {
    color: var(--white);
}

.service-card-items.style-2.active .icon::before {
    background-color: var(--white);
    background-image: initial;
}

.service-card-items.style-2.active .icon i {
    color: var(--theme);
}

.service-card-items.style-2.active::before {
    opacity: 1;
    visibility: visible;
}

.service-card-items.style-2.active::after {
    opacity: 1;
    visibility: visible;
    top: 37%;
}

.service-card-items.style-2.active .content h5 a {
    color: var(--white);
}

.service-provide {
    position: relative;
}

@media (max-width: 1600px) {
    .service-provide {
        overflow: hidden;
    }
}

.service-provide .shape-image {
    position: absolute;
    right: 0;
    top: -70%;
    z-index: -1;
}

@media (max-width: 1600px) {
    .service-provide .shape-image {
        display: none;
    }
}

.service-provide-items {
    position: relative;
    padding: 50px 50px;
    box-shadow: var(---box-shadow);
    margin-top: 60px;
    background-color: var(--white);
    transition: all 0.4s ease-in-out;
}

.service-provide-items .icon {
    width: 120px;
    height: 120px;
    line-height: 130px;
    border-radius: 50%;
    background-color: var(--theme);
    text-align: center;
    margin: 0 auto;
    font-size: 60px;
    color: var(--white);
    margin-top: -100px;
    transition: all 0.4s ease-in-out;
    transition: transform 500ms ease;
}

@media (max-width: 575px) {
    .service-provide-items .icon {
        width: 90px;
        height: 90px;
        line-height: 100px;
        font-size: 42px;
    }
}

.service-provide-items .content {
    margin-top: 25px;
}

@media (max-width: 575px) {
    .service-provide-items .content {
        margin-top: 20px;
    }
}

.service-provide-items .content h5 {
    margin-bottom: 20px;
}

@media (max-width: 575px) {
    .service-provide-items .content h5 {
        margin-bottom: 10px;
    }
}

.service-provide-items .content h5 a:hover {
    color: var(--theme);
}

.service-provide-items .arrow-icon {
    margin-top: 30px;
}

@media (max-width: 575px) {
    .service-provide-items .arrow-icon {
        margin-top: 20px;
    }
}

.service-provide-items .arrow-icon a i {
    font-size: 28px;
    color: var(--theme);
    transition: all 0.4s ease-in-out;
}

.service-provide-items::before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    content: "";
    background-color: var(--theme);
    transform: scale(1, 0);
    transition: transform 500ms ease;
    transform-origin: bottom center;
    z-index: -1;
}

.service-provide-items::after {
    position: absolute;
    top: 0px;
    right: -120px;
    width: 100%;
    height: 100%;
    background-image: url(/website/img/service/shape.webp);
    background-repeat: no-repeat;
    background-size: cover;
    content: "";
    z-index: -1;
    transition: all 0.4s ease-in-out;
}

.service-provide-items:hover {
    background-color: initial;
}

.service-provide-items:hover::before {
    transform: scale(1, 1);
    transform-origin: top center;
}

.service-provide-items:hover::after {
    right: -80px;
    background-color: initial;
}

.service-provide-items:hover .icon {
    color: var(--theme);
    background-color: var(--white);
    background-image: var(--white);
    transform: scaleX(-1);
}

.service-provide-items:hover .content h5 a {
    color: var(--white);
}

.service-provide-items:hover .content p {
    color: var(--white);
}

.service-provide-items:hover .content .arrow-icon a i {
    color: var(--white);
}

.service-provide-items.active {
    background-color: initial;
}

.service-provide-items.active::before {
    transform: scale(1, 1);
    transform-origin: top center;
}

.service-provide-items.active .icon {
    color: var(--theme);
    background-color: var(--white);
    background-image: var(--white);
}

.service-provide-items.active .content h5 a {
    color: var(--white);
}

.service-provide-items.active .content p {
    color: var(--white);
}

.service-provide-items.active .content .arrow-icon a i {
    color: var(--white);
}

.service-productive-wrapper {
    position: relative;
}

.service-productive-wrapper .service-productive-image {
    width: 500px;
    height: 751px;
    position: absolute;
    top: -248px;
    left: 0;
}

@media (max-width: 1600px) {
    .service-productive-wrapper .service-productive-image {
        position: initial;
        top: initial;
    }
}

@media (max-width: 991px) {
    .service-productive-wrapper .service-productive-image {
        height: 600px;
    }
}

@media (max-width: 767px) {
    .service-productive-wrapper .service-productive-image {
        height: 520px;
    }
}

@media (max-width: 575px) {
    .service-productive-wrapper .service-productive-image {
        height: 400px;
    }
}

.service-productive-wrapper .icon-items-area {
    margin-top: 40px;
    display: flex;
    align-items: center;
    gap: 90px;
}

@media (max-width: 767px) {
    .service-productive-wrapper .icon-items-area {
        gap: 50px;
    }
}

@media (max-width: 575px) {
    .service-productive-wrapper .icon-items-area {
        margin-top: 20px;
        flex-wrap: wrap;
        gap: 30px;
    }
}

.service-productive-wrapper .icon-items-area .icon-item {
    gap: 25px;
}

.service-productive-wrapper .icon-items-area .icon-item .icon {
    font-size: 42px;
    color: var(--theme);
    padding: 15px 20px;
    background-color: var(--bg);
    transition: all 0.4s ease-in-out;
}

.service-productive-wrapper .icon-items-area .icon-item:hover .icon {
    background-color: var(--theme);
    color: var(--white);
}

.service-productive-wrapper .icon-items-area .icon-item.active .icon {
    background-color: var(--theme);
    color: var(--white);
}

.service-productive-wrapper h6 {
    margin-top: 40px;
    font-size: 20px;
    color: var(--theme);
}

@media (max-width: 575px) {
    .service-productive-wrapper h6 {
        margin-top: 20px;
    }
}

.service-productive-wrapper .author-items {
    display: flex;
    align-items: center;
    gap: 60px;
    margin-top: 50px;
}

@media (max-width: 575px) {
    .service-productive-wrapper .author-items {
        margin-top: 30px;
        flex-wrap: wrap;
        gap: 30px;
    }
}

.service-productive-wrapper.style-2 {
    position: relative;
    padding-top: 80px;
}

.service-productive-wrapper.style-2 .circle-shape {
    top: 25%;
}

.service-productive-wrapper.style-2::after {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    content: "";
    background-color: var(--bg);
    top: -28%;
    width: 32%;
    height: 80%;
}

.service-productive-wrapper.style-2 .productive-image-2 {
    margin-right: 60px;
    position: relative;
    z-index: 9;
}

.service-productive-wrapper.style-2 .productive-image-2 img {
    width: 100%;
    height: 100%;
}

@media (max-width: 1199px) {
    .service-productive-wrapper.style-2 .productive-image-2 {
        margin-right: 20px;
    }
}

@media (max-width: 991px) {
    .service-productive-wrapper.style-2 .productive-image-2 {
        margin-right: 0;
    }
}

.service-productive {
    position: relative;
}

.service-productive .circle-shape {
    position: absolute;
    top: 10%;
    right: 5%;
}

.service-productive .circle-shape .text-circle {
    animation: cir36 10s linear infinite;
}

@media (max-width: 1600px) {
    .service-productive .circle-shape {
        right: 0;
        top: 4%;
    }
}

@media (max-width: 1199px) {
    .service-productive .circle-shape {
        display: none;
    }
}

.service-details-wrapper .service-details-content h2 {
    font-size: 48px;
    margin-bottom: 20px;
}

@media (max-width: 575px) {
    .service-details-wrapper .service-details-content h2 {
        font-size: 32px;
    }
}

.service-details-wrapper .service-details-content .details-image {
    height: 400px;
    margin-bottom: 30px;
}

@media (max-width: 575px) {
    .service-details-wrapper .service-details-content .details-image {
        margin-bottom: 20px;
    }
}

.service-details-wrapper .service-details-content .details-title {
    font-size: 30px;
    margin-bottom: 20px;
}

@media (max-width: 575px) {
    .service-details-wrapper .service-details-content .details-title {
        font-size: 28px;
    }
}

.service-details-wrapper .service-details-content .service-details-list .goal-list ul li {
    color: var(--header);
    font-weight: 500;
}

.service-details-wrapper .service-details-content .service-details-list .goal-list ul li:not(:last-child) {
    margin-bottom: 10px;
}

.service-details-wrapper .service-details-content .service-details-list .goal-list ul li i {
    color: var(--theme);
    font-size: 18px;
}

.project-section {
    position: relative;
}

.project-section::before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    content: "";
    background-color: var(--bg);
    top: 0;
    z-index: -1;
    height: 30%;
}

.project-section .shape-left {
    position: absolute;
    left: 0;
    top: -19%;
}

@media (max-width: 1199px) {
    .project-section .shape-left {
        display: none;
    }
}

.project-section .right-shape {
    position: absolute;
    top: -19.4%;
    right: 0;
    z-index: -1;
    overflow: hidden;
    animation: top-image-bounce 3s infinite ease-in-out;
}

@media (max-width: 1199px) {
    .project-section .right-shape {
        display: none;
    }
}

.project-items {
    margin-top: 30px;
}

.project-items .project-image {
    overflow: hidden;
    position: relative;
    transition: all 0.4s ease-in-out;
    display: block;
}

@media (max-width: 575px) {
    .project-items .project-image {
        height: 450px;
    }
}

.project-items .project-image img {
    width: 100%;
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
}

.project-items .project-image::before {
    position: absolute;
    inset: 0;
    width: 100%;
    height: 0%;
    content: "";
    background-color: rgba(0, 0, 0, 0.5);
    transition: all 0.9s;
}

.project-items .project-image .project-content-area {
    position: absolute;
    bottom: 0;
    background-color: var(--theme);
    padding: 30px 25px;
    left: 40px;
    right: 40px;
    bottom: -320px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: all 0.9s ease-in-out;
    opacity: 0;
    visibility: hidden;
}

@media (max-width: 575px) {
    .project-items .project-image .project-content-area {
        left: 20px;
        right: 20px;
    }
}

.project-items .project-image .project-content-area.style-2 {
    left: 135px;
    right: 135px;
    padding: 30px 40px;
}

@media (max-width: 575px) {
    .project-items .project-image .project-content-area.style-2 {
        padding: 30px 25px;
        left: 20px;
        right: 20px;
    }
}

.project-items .project-image .project-content-area .content p {
    color: var(--white);
}

.project-items .project-image .project-content-area .content h4 a {
    color: var(--white);
}

.project-items .project-image .project-content-area .icon {
    width: 55px;
    height: 55px;
    line-height: 60px;
    border-radius: 50%;
    background-color: var(--white);
    color: var(--theme);
    display: inline-block;
    text-align: center;
    font-size: 18px;
}

.project-items:hover .project-image::before {
    height: 100%;
}

.project-items:hover .project-image .project-content-area {
    bottom: 40px;
    opacity: 1;
    visibility: visible;
}

@media (max-width: 575px) {
    .project-items:hover .project-image .project-content-area {
        bottom: 20px;
    }
}

.project-wrapper .swiper-slide.swiper-slide-active .project-items-2 .project-image::after {
    opacity: 0.702;
    visibility: visible;
    transform: scale(1);
}

.project-wrapper .swiper-slide.swiper-slide-active .project-items-2 .project-image .project-content,
.project-wrapper .swiper-slide.swiper-slide-active .project-items-2 .project-image .icon {
    opacity: 1;
    visibility: visible;
    bottom: 40px;
}

.project-wrapper .swiper-slide.swiper-slide-active .project-items-2.style-2 .project-content {
    bottom: 10px;
}

.project-items-2 {
    overflow: hidden;
}

.project-items-2 .project-image {
    position: relative;
}

.project-items-2 .project-image img {
    width: 100%;
    height: 100%;
}

.project-items-2 .project-image::after {
    position: absolute;
    content: "";
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    opacity: 0;
    visibility: hidden;
    transform: scale(0.5);
    transition: 0.5s all ease;
    background-color: rgb(0, 5, 28);
}

@media (max-width: 767px) {
    .project-items-2 .project-image {
        height: 380px;
    }
    .project-items-2 .project-image img {
        -o-object-fit: cover;
        object-fit: cover;
    }
}

@media (max-width: 575px) {
    .project-items-2 .project-image {
        height: 300px;
    }
}

.project-items-2 .project-image .icon {
    width: 55px;
    height: 55px;
    line-height: 60px;
    border-radius: 50%;
    background-color: var(--theme);
    color: var(--white);
    font-size: 22px;
    position: absolute;
    right: 40px;
    text-align: center;
    z-index: 9;
    opacity: 0;
    visibility: hidden;
    bottom: -100px;
    transition: all 0.4s ease-in-out;
}

.project-items-2 .project-image .project-content {
    position: absolute;
    left: 40px;
    z-index: 9;
    opacity: 0;
    visibility: hidden;
    bottom: -100px;
    transition: all 0.4s ease-in-out;
}

.project-items-2 .project-image .project-content p {
    color: var(--white);
}

.project-items-2 .project-image .project-content h4 a {
    color: var(--white);
}

.project-items-2:hover .project-image::after {
    opacity: 0.702;
    visibility: visible;
    transform: scale(1);
}

.project-items-2:hover .project-image .project-content,
.project-items-2:hover .project-image .icon {
    opacity: 1;
    visibility: visible;
    bottom: 40px;
}

.project-items-2.style-2 {
    margin-top: 30px;
}

.project-items-2.style-2 .project-content {
    padding: 30px;
    width: 96%;
    background-color: var(--theme);
    left: 10px;
    -webkit-clip-path: polygon(70% 0, 100% 100%, 99% 100%, 0 96%, 0 0);
    clip-path: polygon(70% 0, 100% 100%, 99% 100%, 0 96%, 0 0);
}

.project-items-2.style-2 .icon {
    background-color: var(--white);
    background-image: none;
    z-index: 999;
}

.project-items-2.style-2 .icon i {
    color: var(--theme);
}

.project-items-2.style-2:hover .project-content {
    bottom: 10px;
}

.project-successful-items {
    margin-top: 30px;
    background-color: var(--bg);
    padding: 45px 40px;
    position: relative;
    overflow: hidden;
    transition: all 0.4s ease-in-out;
}

.project-successful-items::before {
    background-color: var(--theme);
    content: "";
    height: 100%;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    visibility: hidden;
    opacity: 0;
    transition: all 0.3s ease-in-out;
}

.project-successful-items .icon {
    font-size: 28px;
    color: var(--theme);
    position: relative;
    z-index: 9;
    transition: all 0.4s ease-in-out;
}

.project-successful-items .content {
    margin-top: 15px;
    position: relative;
    z-index: 9;
}

.project-successful-items .counter-text {
    position: absolute;
    bottom: -20%;
    left: 40%;
}

@media (max-width: 575px) {
    .project-successful-items .counter-text {
        bottom: -15%;
    }
}

.project-successful-items .counter-text h2 {
    font-size: 100px;
    color: var(--header);
    opacity: 0.5;
    font-family: "DM Sans", sans-serif;
}

@media (max-width: 575px) {
    .project-successful-items .counter-text h2 {
        font-size: 80px;
    }
}

.project-successful-items:hover::before {
    visibility: visible;
    opacity: 1;
}

.project-successful-items:hover .icon {
    color: var(--white);
}

.project-successful-items:hover .content h5 {
    color: var(--white);
}

.project-successful-items:hover .counter-text h2 {
    color: var(--white);
}

.project-successful-items.active::before {
    visibility: visible;
    opacity: 1;
}

.project-successful-items.active .icon {
    color: var(--white);
}

.project-successful-items.active .content h5 {
    color: var(--white);
}

.project-successful-items.active .counter-text h2 {
    color: var(--white);
}

.project-section-3 {
    position: relative;
}

.project-section-3::before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    content: "";
    background-color: var(--bg);
    height: 50%;
}

.project-section-3 .shape-image {
    position: absolute;
    top: 0;
    left: 3%;
    z-index: 9;
    animation: rounded 5s linear infinite;
}

@media (max-width: 1199px) {
    .project-section-3 .shape-image {
        display: none;
    }
}

.project-section-3 .shape-image-2 {
    position: absolute;
    right: 0;
    top: 0;
}

@media (max-width: 1199px) {
    .project-section-3 .shape-image-2 {
        display: none;
    }
}

@media (max-width: 1199px) {
    .project-section-3 .line-area {
        display: none;
    }
}

.project-section-3 .line-area span {
    position: absolute;
    content: "";
    width: 1px;
    height: 100%;
    top: 0;
    left: 26%;
    background-color: #efefef;
    z-index: -1;
}

.project-section-3 .line-area span:nth-child(2) {
    left: 50%;
}

.project-section-3 .line-area span:nth-child(3) {
    left: 80%;
}

.project-section-4 {
    position: relative;
}

.project-section-4::before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    content: "";
    background-color: var(--bg);
    height: 60%;
}

.project-section-4 .shape-image {
    position: absolute;
    top: 0;
    left: 0;
}

@media (max-width: 1199px) {
    .project-section-4 .shape-image {
        display: none;
    }
}

.project-wrapper-2 {
    padding: 0 170px;
}

@media (max-width: 1600px) {
    .project-wrapper-2 {
        padding: 0;
    }
}

.project-wrapper-2 .project-items-3 {
    margin-top: 30px;
    overflow: hidden;
}

.project-wrapper-2 .project-items-3 .project-image {
    height: 472px;
    position: relative;
    z-index: 9;
}

.project-wrapper-2 .project-items-3 .project-image::before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    content: "";
    background-color: rgba(0, 0, 0, 0.5);
    transform: scale(1, 0);
    transition: transform 800ms ease;
    transform-origin: bottom center;
    z-index: -1;
}

@media (max-width: 575px) {
    .project-wrapper-2 .project-items-3 .project-image {
        height: 350px;
    }
}

.project-wrapper-2 .project-items-3 .project-image .project-content {
    position: absolute;
    bottom: -60px;
    left: 45px;
    z-index: 9;
    opacity: 0;
    visibility: hidden;
    transition: all 0.4s ease-in-out;
}

.project-wrapper-2 .project-items-3 .project-image .project-content::before {
    position: absolute;
    bottom: -50px;
    left: -50px;
    content: "";
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
    background-image: url(/website/img/shape-4.webp);
    width: 370px;
    height: 281px;
    z-index: -1;
}

.project-wrapper-2 .project-items-3 .project-image .project-content .icon {
    width: 50px;
    height: 50px;
    line-height: 50px;
    border-radius: 50%;
    text-align: center;
    background-color: var(--header);
    display: inline-block;
    color: var(--white);
    margin-bottom: 20px;
}

.project-wrapper-2 .project-items-3 .project-image .project-content p {
    color: var(--white);
    margin-bottom: 5px;
}

.project-wrapper-2 .project-items-3 .project-image .project-content h4 a {
    color: var(--white);
}

.project-wrapper-2 .project-items-3:hover .project-image::before {
    transform: scale(1, 1);
    transform-origin: top center;
}

.project-wrapper-2 .project-items-3:hover .project-image .project-content {
    opacity: 1;
    visibility: visible;
    bottom: 45px;
}

.project-wrapper .project-details-image {
    height: 540px;
}

.project-wrapper .catagory-wrapper {
    display: flex;
    align-items: center;
    gap: 170px;
    justify-content: center;
    margin-top: 30px;
}

.project-wrapper .catagory-wrapper .catagory-content p {
    text-transform: uppercase;
}

.project-wrapper .catagory-wrapper .catagory-content h5 {
    font-size: 18px;
    margin-top: 10px;
}

.project-wrapper .catagory-wrapper .social-icon {
    gap: 15px;
}

.project-wrapper .catagory-wrapper .social-icon a {
    width: 40px;
    height: 40px;
    line-height: 40px;
    display: inline-block;
    border-radius: 50%;
    text-align: center;
    color: var(--white);
}

.project-wrapper .catagory-wrapper .social-icon a.color-1 {
    background-color: rgb(70, 97, 197);
}

.project-wrapper .catagory-wrapper .social-icon a.color-2 {
    background-color: rgb(29, 161, 242);
}

.project-wrapper .catagory-wrapper .social-icon a.color-3 {
    background-color: rgb(255, 46, 46);
}

.project-wrapper .catagory-wrapper .social-icon a.color-4 {
    background-color: rgb(0, 115, 176);
}

.project-wrapper .project-details-content {
    margin-top: 50px;
    border-bottom: 1px solid var(--border);
}

.project-wrapper .project-details-content h2 {
    font-size: 48px;
}

.project-wrapper .project-details-content .tag-share-wrap {
    margin-top: 40px;
    padding-bottom: 30px;
}

.project-wrapper .prev-next-btns {
    margin-top: 30px;
}

.project-wrapper .prev-next-btns .prev-button {
    display: flex;
    align-items: center;
    gap: 25px;
}

.project-wrapper .prev-next-btns .prev-button .icon {
    color: var(--theme);
    font-size: 18px;
}

.project-wrapper .prev-next-btns .prev-button .content h5 {
    font-size: 18px;
}

.project-wrapper .prev-next-btns .next-button {
    display: flex;
    align-items: center;
    gap: 25px;
}

.project-wrapper .prev-next-btns .next-button .icon {
    color: var(--theme);
    font-size: 18px;
}

.project-wrapper .prev-next-btns .next-button .content {
    text-align: right;
}

.project-wrapper .prev-next-btns .next-button .content h5 {
    font-size: 18px;
}

::-webkit-scrollbar {
    width: 4px;
    height: 4px;
}

/* Track */

::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px var(--theme);
    border-radius: 5px;
}

/* Handle */

::-webkit-scrollbar-thumb {
    background: var(--theme);
    border-radius: 10px;
}

.fix {
    overflow: hidden;
}

.ralt {
    position: relative;
}

.ml-100 {
    margin-left: 100px;
}

.ripple {
    position: relative;
}

.ripple::before,
.ripple::after {
    position: absolute;
    left: 50%;
    top: 50%;
    width: 60px;
    height: 60px;
    transform: translateX(-50%) translateY(-50%);
    border-radius: 50%;
    -ms-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.5);
    -o-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.5);
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.5);
    animation: rippleOne 3s infinite;
}

.ripple::before {
    animation-delay: 0.9s;
    content: "";
    position: absolute;
    right: 0;
    bottom: 0;
}

.ripple::after {
    animation-delay: 0.6s;
    content: "";
    position: absolute;
    right: 0;
    bottom: 0;
}

.swiper-dot {
    margin-bottom: -7px;
}

.swiper-dot .swiper-pagination-bullet {
    border: 2px solid var(--header);
    width: 12px;
    height: 12px;
    background-color: transparent;
    border-radius: 0px;
}

.swiper-dot .swiper-pagination-bullet.swiper-pagination-bullet-active {
    background-color: var(--theme);
    transition: 0.6s;
    position: relative;
    border: 2px solid var(--theme);
}

.swiper-dot.style-2 .swiper-pagination-bullet {
    border: none;
    width: 20px;
    height: 20px;
    background-color: var(--header);
    border-radius: 50%;
    opacity: 1;
}

.swiper-dot.style-2 .swiper-pagination-bullet.swiper-pagination-bullet-active {
    background-color: var(--theme);
    border: none;
}

.array-button {
    position: relative;
    z-index: 99;
}

.array-button .array-prev {
    width: 60px;
    height: 60px;
    line-height: 60px;
    border-radius: 50%;
    background-color: var(--white);
    color: var(--theme2);
    font-size: 18px;
    font-weight: 600;
    transition: all 0.4s ease-in-out;
}

@media (max-width: 575px) {
    .array-button .array-prev {
        width: 45px;
        height: 45px;
        line-height: 45px;
    }
}

.array-button .array-prev:hover {
    background-color: var(--theme);
    color: var(--white);
}

.array-button .array-next {
    width: 60px;
    height: 60px;
    line-height: 60px;
    border-radius: 50%;
    background-color: var(--theme);
    color: var(--white);
    font-size: 18px;
    font-weight: 600;
    transition: all 0.4s ease-in-out;
}

@media (max-width: 575px) {
    .array-button .array-next {
        width: 45px;
        height: 45px;
        line-height: 45px;
    }
}

.array-button .array-next:hover {
    background-color: var(--white);
    color: var(--theme);
}

.mt-10 {
    margin-top: 10px;
}

@media (max-width: 575px) {
    br {
        display: none;
    }
}

/* background */

.bg-cover {
    background-repeat: no-repeat;
    background-size: cover;
    position: relative;
    background-position: center;
}

.nice-select {
    color: var(--text);
    background-color: var(--bg);
    font-size: 16px;
    width: unset;
    outline: none;
    padding: 18px 20px;
    border: none;
    transition: all 0.4s ease-in-out;
}

@media (max-width: 575px) {
    .nice-select {
        padding: 15px 20px;
    }
}

.nice-select span {
    color: var(--text);
    font-size: 16px;
    margin-bottom: 0 !important;
}

.nice-select:hover::after {
    border-bottom: 1px solid var(--theme);
    border-right: 1px solid var(--theme);
}

.nice-select option {
    color: var(--text);
}

.nice-select .current {
    margin-right: 12px;
}

.nice-select:after {
    right: 23px;
    border-bottom: 1px solid var(--header);
    border-right: 1px solid var(--header);
    width: 10px;
    height: 10px;
}

.nice-select.open .list {
    background: var(--bg);
    margin-top: 16px;
    width: 100%;
    text-transform: capitalize;
    color: var(--text);
}

.nice-select .option.selected.focus {
    background: var(--bg);
    outline: none;
    color: var(--theme);
    text-transform: capitalize;
    font-size: 16px;
}

.nice-select .option {
    border: none;
}

.nice-select .option:hover {
    background: transparent;
}

.head-color {
    color: var(--header);
}

.footer-bg {
    background-color: rgb(0, 5, 28);
}

.border-none {
    border-top: none !important;
}

.overlay-color {
    position: relative;
}

.overlay-color::before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    content: "";
    background-color: var(--black);
    opacity: 0.3;
}

.single-team-items {
    margin-top: 30px;
    overflow: hidden;
}

.single-team-items .team-image {
    position: relative;
}

.single-team-items .team-image img {
    width: 100%;
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
}

@media (max-width: 575px) {
    .single-team-items .team-image {
        height: 300px;
    }
}

.single-team-items .team-image::before {
    background: linear-gradient(to bottom, rgba(99, 92, 92, 0) 0%, #292930 100%);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    content: "";
    overflow: hidden;
    opacity: 0;
    transition: all 0.3s ease-in-out;
    visibility: hidden;
    content: "";
}

.single-team-items .team-image .team-content {
    position: absolute;
    bottom: -100px;
    left: 40px;
    overflow: hidden;
    opacity: 0;
    visibility: hidden;
    transition: all 0.6s ease-in-out;
}

.single-team-items .team-image .team-content h4 a {
    color: var(--white);
}

.single-team-items .team-image .team-content h4 a:hover {
    color: var(--theme);
}

.single-team-items .team-image .team-content p {
    color: var(--white);
}

.single-team-items .team-image .team-content .social-icon {
    gap: 15px;
    margin-top: 20px;
}

.single-team-items .team-image .team-content .social-icon a {
    width: 35px;
    height: 35px;
    line-height: 35px;
    text-align: center;
    background-color: var(--white);
    border-radius: 50%;
    color: var(--header);
}

.single-team-items .team-image .team-content .social-icon a:hover {
    background-color: var(--theme);
    color: var(--white);
}

.single-team-items:hover .team-image::before {
    visibility: visible;
    opacity: 1;
}

.single-team-items:hover .team-content {
    bottom: 40px;
    visibility: visible;
    opacity: 1;
}

.single-team-items.active .team-image::before {
    visibility: visible;
    opacity: 1;
}

.single-team-items.active .team-content {
    bottom: 40px;
    visibility: visible;
    opacity: 1;
}

.single-team-items.active .team-content .social-icon a.active {
    background-color: var(--theme);
    color: var(--white);
}

.text-area {
    padding: 15px 30px;
    background-color: var(--bg);
    max-width: 520px;
    margin: 0 auto;
}

.text-area a {
    font-weight: 500;
    color: var(--theme);
    text-decoration: underline;
}

.team-card-items {
    margin-top: 30px;
    overflow: hidden;
}

.team-card-items .team-image {
    position: relative;
    height: 540px;
}

@media (max-width: 575px) {
    .team-card-items .team-image {
        height: 400px;
    }
}

.team-card-items .team-image::before {
    background: linear-gradient(to bottom, rgba(99, 92, 92, 0) 0%, #292930 100%);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    content: "";
    overflow: hidden;
    opacity: 0;
    transition: all 0.3s ease-in-out;
    visibility: hidden;
    content: "";
}

.team-card-items .team-image .team-content {
    padding: 30px 20px;
    background-color: var(--white);
    position: absolute;
    bottom: -40px;
    left: 40px;
    width: 81%;
    transition: all 0.4s ease-in-out;
    opacity: 0;
    visibility: hidden;
}

.team-card-items .team-image .team-content h4 {
    margin-bottom: 5px;
}

.team-card-items .team-image .team-content h4 a:hover {
    color: var(--theme);
}

.team-card-items:hover .team-image::before {
    visibility: visible;
    opacity: 1;
}

.team-card-items:hover .team-content {
    visibility: visible;
    opacity: 1;
    bottom: 40px;
}

.team-card-items.active .team-image::before {
    visibility: visible;
    opacity: 1;
}

.team-card-items.active .team-content {
    visibility: visible;
    opacity: 1;
    bottom: 40px;
}

.team-card-items.style-2 .team-image .team-content {
    display: none;
}

.team-card-items.style-2 .team-image .team-content-2 {
    position: absolute;
    bottom: 40px;
    left: 40px;
    z-index: 9;
}

.team-card-items.style-2 .team-image .team-content-2 h4 a {
    color: var(--white);
}

.team-card-items.style-2 .team-image .team-content-2 h4 a:hover {
    color: var(--theme);
}

.team-card-items.style-2 .team-image .team-content-2 p {
    color: var(--white);
}

.team-card-items.style-2 .team-image .social-profile {
    position: absolute;
    right: 40px;
    bottom: 40px;
    content: "";
    transition: all 0.4s ease-in-out;
    z-index: 2;
    display: inline-block;
    overflow: hidden;
}

.team-card-items.style-2 .team-image .social-profile ul {
    transform: translateY(100px);
    transition: all 0.6s ease-in-out;
    opacity: 0;
    visibility: hidden;
}

.team-card-items.style-2 .team-image .social-profile ul li a {
    width: 38px;
    height: 38px;
    line-height: 38px;
    text-align: center;
    font-size: 16px;
    display: block;
    background: var(--white);
    color: var(--header);
    transition: all 0.4s ease-in-out;
    text-align: center;
    margin: 0 auto;
}

.team-card-items.style-2 .team-image .social-profile ul li a:hover {
    color: var(--theme);
}

.team-card-items.style-2 .team-image .social-profile .plus-btn {
    z-index: 2;
    cursor: pointer;
    width: 38px;
    height: 38px;
    line-height: 38px;
    text-align: center;
    font-size: 18px;
    display: inline-block;
    background: var(--theme);
    color: var(--white);
    transition: all 0.3s ease-in-out;
}

.team-card-items.style-2 .team-image .social-profile:hover ul {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
}

.team-card-items.style-2 .team-image .social-profile.active a {
    opacity: 1;
    visibility: visible;
}

.team-card-items.style-2.active .team-image .social-profile ul {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
}

.team-details-wrapper .team-details-image-area {
    position: relative;
    padding-top: 60px;
}

@media (max-width: 991px) {
    .team-details-wrapper .team-details-image-area {
        padding-top: 0;
    }
}

.team-details-wrapper .team-details-image-area::before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    content: "";
    background-color: var(--bg);
    height: 85%;
}

@media (max-width: 991px) {
    .team-details-wrapper .team-details-image-area::before {
        display: none;
    }
}

.team-details-wrapper .team-details-image-area .text {
    font-size: 120px;
    text-transform: uppercase;
    color: var(--theme);
    position: relative;
    z-index: 1;
    transform: rotate(-90deg);
    position: absolute;
    top: 25%;
    left: -8px;
}

@media (max-width: 1199px) {
    .team-details-wrapper .team-details-image-area .text {
        left: -70px;
    }
}

@media (max-width: 991px) {
    .team-details-wrapper .team-details-image-area .text {
        display: none;
    }
}

.team-details-wrapper .team-details-image-area .details-iamge {
    height: 502px;
    position: relative;
    z-index: 9;
}

.team-details-wrapper .team-details-image-area .right-content {
    position: relative;
    z-index: 9;
}

.team-details-wrapper .team-details-image-area .right-content h2 {
    font-size: 40px;
    margin-bottom: 5px;
}

@media (max-width: 991px) {
    .team-details-wrapper .team-details-image-area .right-content h2 {
        font-size: 32px;
    }
}

.team-details-wrapper .team-details-image-area .right-content p {
    color: var(--theme);
}

.team-details-wrapper .team-details-image-area .right-content .list {
    margin-top: 30px;
}

@media (max-width: 991px) {
    .team-details-wrapper .team-details-image-area .right-content .list {
        margin-top: 20px;
    }
}

.team-details-wrapper .team-details-image-area .right-content .list h5 {
    font-size: 18px;
    font-weight: 500;
}

.team-details-wrapper .team-details-image-area .right-content .list h5 a,
.team-details-wrapper .team-details-image-area .right-content .list h5 span {
    color: var(--text);
    font-weight: normal;
}

.team-details-wrapper .team-details-image-area .right-content .social-icon {
    gap: 15px;
    margin-top: 40px;
}

@media (max-width: 991px) {
    .team-details-wrapper .team-details-image-area .right-content .social-icon {
        margin-top: 30px;
    }
}

.team-details-wrapper .team-details-image-area .right-content .social-icon a {
    width: 40px;
    height: 40px;
    line-height: 40px;
    display: inline-block;
    border-radius: 50%;
    text-align: center;
    color: var(--white);
}

.team-details-wrapper .team-details-image-area .right-content .social-icon a.color-1 {
    background-color: rgb(70, 97, 197);
}

.team-details-wrapper .team-details-image-area .right-content .social-icon a.color-2 {
    background-color: rgb(29, 161, 242);
}

.team-details-wrapper .team-details-image-area .right-content .social-icon a.color-3 {
    background-color: rgb(255, 46, 46);
}

.team-details-wrapper .team-details-image-area .right-content .social-icon a.color-4 {
    background-color: rgb(0, 115, 176);
}

.team-details-wrapper .team-details-content {
    margin-top: -7px;
    margin-bottom: -5px;
}

.team-details-wrapper .team-details-content h2 {
    font-size: 36px;
    margin-bottom: 20px;
}

@media (max-width: 575px) {
    .team-details-wrapper .team-details-content h2 {
        font-size: 28px;
    }
}

.team-details-wrapper .team-details-content .details-information-area {
    margin-top: 40px;
    margin-bottom: 40px;
}

.team-details-wrapper .team-details-content .details-information-area .progress-wrap .pro-items:not(:last-child) {
    margin-bottom: 20px;
}

.team-details-wrapper .team-details-content .details-information-area .progress-wrap .pro-items .pro-head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
}

.team-details-wrapper .team-details-content .details-information-area .progress-wrap .pro-items .pro-head .title {
    font-size: 16px;
}

.team-details-wrapper .team-details-content .details-information-area .progress-wrap .pro-items .pro-head .point {
    font-size: 14px;
    color: var(--text);
}

.team-details-wrapper .team-details-content .details-information-area .progress-wrap .pro-items .progress {
    background: var(--bg);
    justify-content: flex-start;
    border-radius: 100px;
    align-items: center;
    position: relative;
    display: flex;
    height: 6px;
    width: 100%;
}

.team-details-wrapper .team-details-content .details-information-area .progress-wrap .pro-items .progress-value {
    animation: load 3s normal forwards;
    border-radius: 0;
    background: var(--theme);
    height: 6px;
    width: 0;
}

.team-details-wrapper .team-details-content .details-information-area .progress-wrap .pro-items .style-two {
    animation: load2 3s normal forwards;
}

.team-details-wrapper .team-details-content .details-information-area .progress-wrap .pro-items .style-three {
    animation: load3 3s normal forwards;
}

@keyframes load {
    0% {
        width: 0;
    }
    100% {
        width: 90%;
    }
}

@keyframes load2 {
    0% {
        width: 0;
    }
    100% {
        width: 50%;
    }
}

@keyframes load3 {
    0% {
        width: 0;
    }
    100% {
        width: 80%;
    }
}

.team-details-wrapper .team-details-content .details-information-area .information-content-items {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.team-details-wrapper .team-details-content .details-information-area .information-content-items .content-box-area .content h3 {
    margin-bottom: 10px;
}

.single-shop-items {
    overflow: hidden;
    margin-top: 30px;
}

.single-shop-items .shop-image {
    position: relative;
}

.single-shop-items .shop-image img {
    width: 100%;
    height: 100%;
}

.single-shop-items .shop-image .shop-button {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    visibility: hidden;
    transition: all 0.4s ease-in-out;
}

.single-shop-items .shop-content {
    padding: 20px 30px;
}

.single-shop-items .shop-content h4 a:hover {
    color: var(--theme);
}

.single-shop-items .shop-content .price {
    margin-top: 15px;
}

.single-shop-items .shop-content .price p {
    color: var(--theme2);
}

.single-shop-items .shop-content .price .star {
    color: var(--theme);
}

.single-shop-items:hover .shop-image .shop-button {
    opacity: 1;
    visibility: visible;
    top: 50%;
}

.single-shop-items.active .shop-image .shop-button {
    opacity: 1;
    visibility: visible;
    top: 50%;
}

.woocommerce-notices-wrapper {
    margin-bottom: 30px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

@media (max-width: 575px) {
    .woocommerce-notices-wrapper {
        flex-wrap: wrap;
        gap: 30px;
    }
}

.woocommerce-notices-wrapper .product-showing {
    font-size: 16px;
}

.woocommerce-notices-wrapper .woocommerce-right {
    gap: 30px;
}

.woocommerce-notices-wrapper .woocommerce-right .form-clt {
    width: 210px;
}

.woocommerce-notices-wrapper .woocommerce-right .form-clt .nice-select {
    padding: 18px 20px;
    font-size: 16px;
    text-transform: capitalize;
    border-radius: 0;
    background-color: transparent;
    border: 1px solid var(--border);
}

.woocommerce-notices-wrapper .woocommerce-right .form-clt.style-2 {
    width: 260px;
}

.main-cart-wrapper {
    border-radius: 5px;
}

.main-cart-wrapper .cart-wrapper {
    box-shadow: 0px 6px 30px rgba(0, 0, 0, 0.06);
    padding: 40px 40px;
}

.main-cart-wrapper .cart-wrapper .cart-items-wrapper table {
    width: 100%;
}

.main-cart-wrapper .cart-wrapper .cart-items-wrapper table thead {
    border-bottom: 1px solid var(--border);
}

.main-cart-wrapper .cart-wrapper .cart-items-wrapper table thead tr th {
    padding-bottom: 16px;
    color: var(--header);
    text-transform: capitalize;
}

.main-cart-wrapper .cart-wrapper .cart-items-wrapper table tbody .cart-item td {
    border-bottom: 1px solid var(--border);
    padding: 16px 0;
}

.main-cart-wrapper .cart-wrapper .cart-items-wrapper table tbody .cart-item .cart-item-info {
    display: flex;
    align-items: center;
    gap: 16px;
}

.main-cart-wrapper .cart-wrapper .cart-items-wrapper table tbody .cart-item .cart-item-price {
    color: var(--header);
}

.main-cart-wrapper .cart-wrapper .cart-items-wrapper table tbody .cart-item .cart-item-quantity {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    background: var(--bg);
    width: 100px;
    border-radius: 5px;
    padding: 2px 20px;
}

.main-cart-wrapper .cart-wrapper .cart-items-wrapper table tbody .cart-item .cart-item-quantity span {
    display: block;
    color: var(--header);
}

.main-cart-wrapper .cart-wrapper .cart-items-wrapper table tbody .cart-item .cart-item-quantity .cart-item-quantity-controller a {
    display: block;
}

.main-cart-wrapper .cart-wrapper .cart-items-wrapper table tbody .cart-item .cart-item-quantity .cart-item-quantity-controller a i {
    color: var(--header);
}

.main-cart-wrapper .cart-wrapper .cart-items-wrapper table tbody .cart-item .cart-item-remove a i {
    color: var(--header);
}

.main-cart-wrapper .cart-wrapper-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 20px;
}

.main-cart-wrapper .cart-wrapper-footer form {
    border: 1px solid var(--border);
    padding: 8px;
    border-radius: 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.main-cart-wrapper .cart-wrapper-footer form input {
    padding: 5px 5px;
    border: none;
    text-transform: capitalize;
    font-size: 16px;
    outline: none;
    background: transparent;
    color: var(--text);
}

.main-cart-wrapper .cart-wrapper-footer form button {
    outline: none;
    border: none;
}

.main-cart-wrapper .cart-pragh-box {
    margin-top: 24px;
    padding-right: 30px;
    padding-bottom: 30px;
}

.main-cart-wrapper .cart-graph {
    border: 1px solid var(--border);
    padding: 30px 30px;
    border-radius: 5px;
}

.main-cart-wrapper .cart-graph h4 {
    text-align: center;
    color: var(--header);
    margin-bottom: 30px;
}

.main-cart-wrapper .cart-graph ul {
    margin-bottom: 30px;
}

.main-cart-wrapper .cart-graph ul li {
    display: flex;
}

.main-cart-wrapper .cart-graph ul li:not(:last-child) {
    border-bottom: 1px solid var(--border);
}

.main-cart-wrapper .cart-graph ul li span {
    width: 50%;
    color: var(--header);
    font-size: 18px;
    text-transform: capitalize;
    padding: 16px 0;
    font-weight: 500;
}

@media (max-width: 767px) {
    .cart-wrapper {
        overflow-x: scroll;
    }
    .cart-wrapper .cart-items-wrapper {
        width: 700px;
    }
    .cart-wrapper .cart-wrapper-footer {
        width: 700px;
    }
}

.checkout-radio {
    box-shadow: 0px 6px 30px rgba(0, 0, 0, 0.06);
    border-radius: 5px;
    padding: 24px;
}

.checkout-radio .primary-text {
    font-size: 24px;
    font-weight: 500;
    line-height: 150%;
    margin-bottom: 16px;
    color: var(--header);
    text-transform: capitalize;
}

.checkout-radio h4 {
    color: var(--header);
    margin-bottom: 16px;
    font-weight: 600;
}

.checkout-radio .checkout-radio-wrapper .checkout-radio-single .form-check-input {
    border-radius: 50%;
    width: 18px;
    height: 18px;
    box-shadow: none;
    outline: none;
    border: 1px solid var(--border);
    font-weight: 500;
}

.checkout-radio .checkout-radio-wrapper .checkout-radio-single label {
    color: var(--header);
    text-transform: capitalize;
}

.checkout-radio .checkout-radio-wrapper .checkout-radio-single:not(:last-child) {
    margin-bottom: 12px;
}

@media (max-width: 500px) {
    .checkout-radio {
        padding: 10px;
    }
}

.checkout-single-wrapper .checkout-single h4 {
    color: var(--header);
    margin-bottom: 2rem;
    font-weight: 600;
}

.checkout-single-wrapper .checkout-single .checkout-single-form .input-single textarea,
.checkout-single-wrapper .checkout-single .checkout-single-form .input-single .country-select,
.checkout-single-wrapper .checkout-single .checkout-single-form .input-single input {
    width: 100%;
    outline: none;
    box-shadow: none;
    border: 1px solid var(--border);
    border-radius: 5px;
    padding: 12px 24px;
    color: var(--header);
    text-transform: capitalize;
    font-weight: 500;
}

.checkout-single-wrapper .checkout-single .checkout-single-form .input-single label {
    color: var(--header);
    font-size: 18px;
    text-transform: capitalize;
    margin-bottom: 10px;
    font-weight: 500;
}

.checkout-single-wrapper .checkout-single .checkout-single-form .input-single ::-moz-placeholder {
    color: var(--header);
}

.checkout-single-wrapper .checkout-single .checkout-single-form .input-single ::placeholder {
    color: var(--header);
}

.checkout-single-wrapper .checkout-single .checkout-single-form .input-single .nice-select {
    background-color: var(--white);
}

.checkout-single-wrapper .checkout-single .checkout-single-form .input-single .nice-select span {
    font-size: 18px;
    color: var(--header);
    font-weight: 500;
}

.checkout-single-wrapper .checkout-single .checkout-single-form .input-single .nice-select::after {
    border-right: 1px solid var(--header);
    border-bottom: 1px solid var(--header);
}

.checkout-single-wrapper .checkout-single .checkout-single-form .payment {
    color: var(--header);
    margin-bottom: 12px;
    text-transform: capitalize;
}

.checkout-single-wrapper .boxshado-single {
    box-shadow: 0px 6px 30px rgba(0, 0, 0, 0.06);
    border-radius: 5px;
    padding: 32px;
    margin-bottom: 24px;
}

@media (max-width: 575px) {
    .checkout-single-wrapper .boxshado-single {
        padding: 14px;
    }
}

.checkout-single-wrapper .checkout-single-bg {
    box-shadow: 0px 6px 30px rgba(0, 0, 0, 0.06);
    border-radius: 5px;
    padding: 32px;
}

.checkout-single-wrapper .checkout-single-bg .checkout-single-form .input-single textarea,
.checkout-single-wrapper .checkout-single-bg .checkout-single-form .input-single .country-select,
.checkout-single-wrapper .checkout-single-bg .checkout-single-form .input-single input {
    border: 1px solid var(--border);
    background: transparent;
    text-transform: capitalize;
}

@media (max-width: 575px) {
    .checkout-single-wrapper .checkout-single-bg {
        padding: 14px;
    }
}

.checkout-single-wrapper .checkout-single-bg .payment-save {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 20px;
}

.checkout-single-wrapper .checkout-single-bg .payment-save input {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    border: 1px solid var(--border);
    background-color: var(--theme);
    outline: none;
    color: var(--header);
}

.checkout-single-wrapper .checkout-single-bg .payment-save label {
    font-size: 16px;
    font-weight: 500;
    text-transform: capitalize;
}

.shop-details-wrapper .shop-details-image {
    position: relative;
}

.shop-details-wrapper .shop-details-image img {
    width: 100%;
    height: 100%;
}

.shop-details-wrapper .shop-details-image .icon {
    width: 60px;
    height: 60px;
    line-height: 60px;
    border-radius: 50%;
    text-align: center;
    background-color: #e9e9ea;
    color: var(--header);
    display: inline-block;
    position: absolute;
    top: 40px;
    right: 40px;
}

.shop-details-wrapper .product-details-content {
    margin-left: 60px;
}

@media (max-width: 1399px) {
    .shop-details-wrapper .product-details-content {
        margin-left: 30px;
    }
}

@media (max-width: 991px) {
    .shop-details-wrapper .product-details-content {
        margin-left: 0;
    }
}

.shop-details-wrapper .product-details-content .star a {
    color: var(--theme);
    font-size: 16px;
    font-weight: 600;
}

.shop-details-wrapper .product-details-content .star span {
    margin-left: 10px;
}

.shop-details-wrapper .product-details-content .price-list {
    gap: 20px;
    border-bottom: 1px solid var(--border);
    padding-bottom: 30px;
}

.shop-details-wrapper .product-details-content .price-list h3 {
    font-size: 40px;
}

.shop-details-wrapper .product-details-content .cart-wrp {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 15px;
    margin-top: 40px;
    margin-bottom: 40px;
}

.shop-details-wrapper .product-details-content .cart-wrp .cart-quantity {
    padding-right: 15px;
}

.shop-details-wrapper .product-details-content .cart-wrp .cart-quantity input {
    width: 50px;
    height: 40px;
    text-align: center;
    background-color: transparent;
    border: 1px solid var(--border);
    color: var(--header);
}

.shop-details-wrapper .product-details-content .cart-wrp .cart-quantity .minus {
    color: var(--header);
}

.shop-details-wrapper .product-details-content .cart-wrp .cart-quantity .plus {
    color: var(--header);
}

.shop-details-wrapper .product-details-content .cart-wrp .cart-quantity .qty {
    color: var(--header);
}

.shop-details-wrapper .product-details-content .cart-wrp .icon {
    width: 50px;
    height: 40px;
    text-align: center;
    background-color: transparent;
    border: 1px solid var(--border);
    color: var(--header);
    line-height: 40px;
}

.shop-details-wrapper .product-details-content .cart-wrp .theme-btn {
    padding: 16px 45px;
}

.shop-details-wrapper .product-details-content .details-info {
    position: relative;
}

.shop-details-wrapper .product-details-content .details-info:not(:last-child) {
    margin-bottom: 10px;
}

.shop-details-wrapper .product-details-content .details-info span {
    font-size: 16px;
    font-weight: 600;
    text-transform: capitalize;
    position: absolute;
    left: 0;
    top: 2px;
}

.shop-details-wrapper .product-details-content .details-info a {
    font-size: 16px;
    font-weight: 400;
    color: var(--text);
    margin-left: 150px;
    text-transform: capitalize;
}

.shop-details-wrapper .single-tab {
    padding-top: 80px;
}

@media (max-width: 575px) {
    .shop-details-wrapper .single-tab {
        padding-top: 50px;
    }
}

.shop-details-wrapper .single-tab .nav {
    justify-content: center;
    border-bottom: 1px solid var(--border);
    padding-bottom: 20px;
}

.shop-details-wrapper .single-tab .nav .nav-link.active {
    position: relative;
}

.shop-details-wrapper .single-tab .nav .nav-link.active h6 {
    color: var(--theme);
}

.shop-details-wrapper .single-tab .nav .nav-link.active::before {
    position: absolute;
    bottom: -21px;
    left: 0;
    height: 2px;
    width: 100%;
    content: "";
    background: var(--theme);
    transition: 0.3s;
}

@media (max-width: 470px) {
    .shop-details-wrapper .single-tab .nav .nav-link.active::before {
        display: none;
    }
}

.shop-details-wrapper .single-tab .nav .nav-link h6 {
    font-size: 18px;
}

.shop-details-wrapper .single-tab .description-items .description-content {
    margin-right: 50px;
}

@media (max-width: 1399px) {
    .shop-details-wrapper .single-tab .description-items .description-content {
        margin-right: 30px;
    }
}

@media (max-width: 991px) {
    .shop-details-wrapper .single-tab .description-items .description-content {
        margin-right: 0;
    }
}

.shop-details-wrapper .single-tab .description-items .description-content h3 {
    margin-bottom: 15px;
}

.shop-details-wrapper .single-tab .description-items .description-content .description-list-items {
    margin-top: 30px;
}

@media (max-width: 575px) {
    .shop-details-wrapper .single-tab .description-items .description-content .description-list-items {
        flex-wrap: wrap;
        gap: 30px;
    }
}

.shop-details-wrapper .single-tab .description-items .description-content .description-list-items .description-list li {
    font-size: 16px;
    text-transform: capitalize;
    color: var(--header);
    font-weight: 600;
}

.shop-details-wrapper .single-tab .description-items .description-content .description-list-items .description-list li span {
    color: var(--text);
    font-weight: 400;
}

@media (max-width: 767px) {
    .shop-details-wrapper .single-tab .review-items .admin-items {
        justify-content: center;
        gap: 30px;
    }
}

.shop-details-wrapper .single-tab .review-items .admin-items .admin-img {
    width: 100px;
    height: 100px;
}

.shop-details-wrapper .single-tab .review-items .admin-items .admin-img img {
    border-radius: 50%;
}

.shop-details-wrapper .single-tab .review-items .admin-items .content {
    position: relative;
    border: 1px solid var(--border);
}

.shop-details-wrapper .single-tab .review-items .admin-items .content::before {
    position: absolute;
    content: "";
    width: 30px;
    height: 30px;
    background-color: var(--white);
    border-left: 1px solid var(--border);
    border-bottom: 1px solid var(--border);
    transform: rotate(45deg);
    top: 40%;
    left: -16px;
}

@media (max-width: 575px) {
    .shop-details-wrapper .single-tab .review-items .admin-items .content::before {
        display: none;
    }
}

.shop-details-wrapper .single-tab .review-items .admin-items .content .head-content h5 span {
    font-size: 14px;
    margin-left: 30px;
}

.shop-details-wrapper .single-tab .review-items .admin-items .content .star i {
    font-size: 16px;
    color: var(--theme);
}

.shop-details-wrapper .single-tab .review-title .rate-now {
    margin-top: 15px;
    gap: 15px;
}

.shop-details-wrapper .single-tab .review-title .rate-now p {
    font-size: 16px;
    text-transform: capitalize;
}

.shop-details-wrapper .single-tab .review-title .rate-now i {
    font-size: 16px;
    color: var(--theme);
}

.shop-details-wrapper .single-tab .review-form {
    margin-top: 40px;
}

.shop-details-wrapper .single-tab .review-form .form-clt input {
    width: 100%;
    outline: none;
    border: none;
    padding: 18px 35px;
    color: var(--text);
    background-color: var(--bg);
    font-size: 16px;
    text-transform: capitalize;
}

@media (max-width: 575px) {
    .shop-details-wrapper .single-tab .review-form .form-clt input {
        padding: 15px 20px;
    }
}

.shop-details-wrapper .single-tab .review-form .form-clt-big textarea {
    padding: 18px 35px 170px;
    width: 100%;
    outline: none;
    color: var(--text);
    background-color: rgb(245, 245, 245);
    font-size: 14px;
    border: none;
    text-transform: uppercase;
}

@media (max-width: 575px) {
    .shop-details-wrapper .single-tab .review-form .form-clt-big textarea {
        padding: 15px 20px;
    }
}

.faq-section {
    position: relative;
}

@media (max-width: 1199px) {
    .faq-section .line-area {
        display: none;
    }
}

.faq-section .line-area span {
    position: absolute;
    content: "";
    width: 1px;
    height: 100%;
    top: 0;
    left: 26%;
    background-color: #efefef;
    z-index: -1;
}

.faq-section .line-area span:nth-child(2) {
    left: 50%;
}

.faq-section .line-area span:nth-child(3) {
    left: 80%;
}

.faq-wrapper .faq-image {
    height: 710px;
    width: 893px;
    margin-left: -50%;
    position: relative;
}

@media (max-width: 1399px) {
    .faq-wrapper .faq-image {
        margin-left: -70%;
    }
}

@media (max-width: 1199px) {
    .faq-wrapper .faq-image {
        width: initial;
        max-width: 890px;
        margin-left: 0;
    }
}

@media (max-width: 991px) {
    .faq-wrapper .faq-image {
        height: 600px;
    }
}

@media (max-width: 767px) {
    .faq-wrapper .faq-image {
        height: 500px;
    }
}

@media (max-width: 575px) {
    .faq-wrapper .faq-image {
        height: 400px;
    }
}

.faq-wrapper .faq-image .counter-area {
    position: absolute;
    bottom: 10%;
    right: -4%;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

@media (max-width: 991px) {
    .faq-wrapper .faq-image .counter-area {
        right: 0;
    }
}

.faq-wrapper .faq-image .counter-area .counter-items {
    padding: 40px 90px 40px 40px;
    background-color: var(--header);
}

@media (max-width: 767px) {
    .faq-wrapper .faq-image .counter-area .counter-items {
        padding: 35px;
    }
}

@media (max-width: 575px) {
    .faq-wrapper .faq-image .counter-area .counter-items {
        padding: 25px;
    }
}

.faq-wrapper .faq-image .counter-area .counter-items h2 {
    font-size: 48px;
    color: var(--white);
    margin-bottom: 5px;
}

@media (max-width: 575px) {
    .faq-wrapper .faq-image .counter-area .counter-items h2 {
        font-size: 42px;
    }
}

.faq-wrapper .faq-image .counter-area .counter-items p {
    font-size: 18px;
    color: var(--white);
}

@media (max-width: 575px) {
    .faq-wrapper .faq-image .counter-area .counter-items p {
        font-size: 16px;
    }
}

.faq-wrapper .faq-image .counter-area .counter-items.style-2 {
    background-color: var(--theme);
}

.faq-wrapper .faq-image-items {
    position: relative;
}

.faq-wrapper .faq-image-items .faq-image-1 {
    max-width: 350px;
    height: 380px;
}

@media (max-width: 1199px) {
    .faq-wrapper .faq-image-items .faq-image-1 {
        max-width: 320px;
        height: 350px;
    }
}

@media (max-width: 575px) {
    .faq-wrapper .faq-image-items .faq-image-1 {
        max-width: 460px;
        height: 400px;
    }
}

.faq-wrapper .faq-image-items .faq-image-2 {
    max-width: 350px;
    position: absolute;
    top: 28%;
    right: 18%;
    z-index: -1;
}

.faq-wrapper .faq-image-items .faq-image-2 img {
    width: 100%;
}

@media (max-width: 1399px) {
    .faq-wrapper .faq-image-items .faq-image-2 {
        right: 10%;
    }
}

@media (max-width: 1199px) {
    .faq-wrapper .faq-image-items .faq-image-2 {
        max-width: 320px;
        right: 4%;
    }
}

@media (max-width: 991px) {
    .faq-wrapper .faq-image-items .faq-image-2 {
        max-width: 300px;
    }
}

@media (max-width: 767px) {
    .faq-wrapper .faq-image-items .faq-image-2 {
        max-width: 275px;
        right: 0;
    }
}

@media (max-width: 575px) {
    .faq-wrapper .faq-image-items .faq-image-2 {
        width: 210px;
        height: 210px;
        right: 0;
        top: initial;
        bottom: 0;
        z-index: 99;
    }
}

.faq-wrapper .faq-image-items::before {
    position: absolute;
    top: 15px;
    right: 28%;
    width: 65px;
    height: 65px;
    content: "";
    background-color: var(--theme);
    animation: top-image-bounce 3s infinite ease-in-out;
}

@media (max-width: 1399px) {
    .faq-wrapper .faq-image-items::before {
        right: 20%;
    }
}

@media (max-width: 1199px) {
    .faq-wrapper .faq-image-items::before {
        display: none;
    }
}

.faq-wrapper .faq-image-items::after {
    position: absolute;
    bottom: -23%;
    left: 8%;
    width: 65px;
    height: 65px;
    content: "";
    background-color: var(--theme2);
    animation: top-image-bounce-2 3s infinite ease-in-out;
}

@media (max-width: 1199px) {
    .faq-wrapper .faq-image-items::after {
        display: none;
    }
}

.faq-content .accordion-item {
    border: 0;
    margin-top: 20px;
    border-radius: 10px;
    background-color: var(--white);
}

@media (max-width: 575px) {
    .faq-content .accordion-item {
        margin-top: 10px;
    }
}

.faq-content .accordion-item .accordion-header .accordion-button {
    font-weight: 600;
    color: var(--header);
    letter-spacing: -0.2px;
    border: 0;
    border-radius: 0;
    box-shadow: none;
    background-color: transparent;
    padding: 10px 0;
}

.faq-content .accordion-item .accordion-header .accordion-button::after {
    content: "\f067";
    background: transparent;
    font-family: "Font Awesome 5 Pro";
    font-weight: 300;
    transition: all 0.3s ease-in-out !important;
    width: 20px;
    height: 20px;
    line-height: 20px;
    background-color: var(--theme);
    text-align: center;
    font-size: 14px;
    color: var(--white);
}

.faq-content .accordion-item .accordion-header .accordion-button:not(.collapsed)::after {
    content: "\f068";
    background: transparent;
    font-family: "Font Awesome 5 Pro";
    font-weight: 300;
    background-color: var(--header);
}

.faq-content .accordion-item .accordion-collapse .accordion-body {
    padding-right: 100px;
    padding-left: 0;
    padding-top: 5px;
    color: var(--text);
}

@media (max-width: 1199px) {
    .faq-content .accordion-item .accordion-collapse .accordion-body {
        padding-right: 0;
    }
}

.faq-content.style-2 .accordion-item {
    margin-top: 10px;
}

.faq-content.style-3 {
    margin-top: 50px;
}

.faq-content.style-3 .collapse {
    border-top: 1px solid var(--border);
    margin-top: 20px;
    padding-top: 20px;
}

.faq-content.style-3 .accordion-item {
    background-color: var(--white);
    box-shadow: var(---box-shadow);
    padding: 18px 30px;
    border-radius: 0;
}

.faq-content.style-3 .accordion-item .accordion-header .accordion-button {
    padding: 10px 45px;
}

.faq-content.style-3 .accordion-item .accordion-header .accordion-button::after {
    position: absolute;
    top: 10px;
    left: 0;
    width: 25px;
    height: 25px;
    line-height: 25px;
    border-radius: 50%;
}

.faq-content.style-3 .accordion-item .accordion-header .accordion-button:not(.collapsed)::after {
    background-color: var(--theme);
}

.faq-wrapper-2 .faq-items {
    padding: 80px 100px;
    background-color: var(--bg);
    display: flex;
    align-items: center;
    justify-content: space-between;
    overflow: hidden;
    margin-bottom: 50px;
}

@media (max-width: 767px) {
    .faq-wrapper-2 .faq-items {
        padding: 80px 30px;
        justify-content: center;
        gap: 30px;
        flex-wrap: wrap;
        text-align: center;
    }
}

.faq-wrapper-2 .faq-items .faq-image {
    margin-bottom: -80px;
    animation: top-image-bounce 3s infinite ease-in-out;
}

.faq-wrapper-2 .faq-sidebar .search-widget form {
    width: 100%;
    position: relative;
}

.faq-wrapper-2 .faq-sidebar .search-widget form input {
    background-color: transparent;
    font-size: 16px;
    padding: 20px;
    width: 100%;
    border: none;
    color: var(--text);
    border: 1px solid var(--border);
}

.faq-wrapper-2 .faq-sidebar .search-widget form button {
    position: absolute;
    right: 20px;
    top: 0;
    font-size: 18px;
    height: 100%;
    color: var(--header);
}

.faq-wrapper-2 .faq-sidebar .side-post {
    height: 218px;
    margin-top: 40px;
    position: relative;
}

.faq-wrapper-2 .faq-sidebar .side-post::before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    content: "";
    background-color: rgb(42, 40, 51);
    opacity: 0.451;
}

.faq-wrapper-2 .faq-sidebar .side-post h3 {
    position: relative;
    z-index: 9;
    color: var(--white);
    padding: 40px;
    font-size: 30px;
}

.main-sidebar .service-sidebar-widget {
    margin-bottom: 35px;
    padding: 40px;
    background-color: var(--bg);
}

.main-sidebar .service-sidebar-widget .wid-title {
    margin-bottom: 30px;
}

.main-sidebar .service-sidebar-widget .service-category a {
    position: relative;
    display: inline-block;
    padding: 18px 20px;
    background-color: var(--white);
    width: 100%;
    font-weight: 600;
    z-index: 9;
}

.main-sidebar .service-sidebar-widget .service-category a:not(:last-child) {
    margin-bottom: 10px;
}

.main-sidebar .service-sidebar-widget .service-category a i {
    font-size: 18px;
    color: var(--header);
    position: absolute;
    top: 25px;
    right: 20px;
}

.main-sidebar .service-sidebar-widget .service-category a::before {
    background-color: var(--theme);
    content: "";
    height: 100%;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    visibility: hidden;
    opacity: 0;
    transition: all 0.3s ease-in-out;
    z-index: -1;
}

.main-sidebar .service-sidebar-widget .service-category a:hover {
    color: var(--white);
}

.main-sidebar .service-sidebar-widget .service-category a:hover i {
    color: var(--white);
}

.main-sidebar .service-sidebar-widget .service-category a:hover::before {
    visibility: visible;
    opacity: 1;
}

.main-sidebar .service-sidebar-widget .service-category a.active {
    color: var(--white);
}

.main-sidebar .service-sidebar-widget .service-category a.active i {
    color: var(--white);
}

.main-sidebar .service-sidebar-widget .service-category a.active::before {
    visibility: visible;
    opacity: 1;
}

.main-sidebar .service-sidebar-widget .service-info {
    display: flex;
    align-items: center;
    gap: 30px;
}

.main-sidebar .service-sidebar-widget .service-info .icon {
    font-size: 42px;
    color: var(--theme);
}

.main-sidebar .service-sidebar-widget .service-info .content h5 {
    font-size: 18px;
    margin-top: 5px;
    font-weight: 500;
}

.main-sidebar .service-post {
    padding: 90px 40px;
    position: relative;
}

.main-sidebar .service-post .content {
    text-align: center;
    z-index: 9;
    position: relative;
}

.main-sidebar .service-post .content h3 {
    font-size: 32px;
    color: var(--white);
    margin-bottom: 30px;
}

.product-details-wrapper .product-image-items .tab-content .tab-pane .product-image {
    position: relative;
}

.product-details-wrapper .product-image-items .tab-content .tab-pane .product-image img {
    width: 100%;
    height: 100%;
}

.product-details-wrapper .product-image-items .tab-content .tab-pane .product-image .icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 130px;
    height: 130px;
    line-height: 130px;
    border-radius: 50%;
    background-color: var(--theme3);
    text-align: center;
}

@media (max-width: 991px) {
    .product-details-wrapper .product-image-items .tab-content .tab-pane .product-image .icon {
        width: 90px;
        height: 90px;
        line-height: 90px;
        font-size: 16px;
    }
}

@media (max-width: 575px) {
    .product-details-wrapper .product-image-items .tab-content .tab-pane .product-image .icon {
        width: 70px;
        height: 70px;
        line-height: 70px;
        font-size: 15px;
    }
}

.product-details-wrapper .product-image-items .tab-content .tab-pane .product-image .icon i {
    color: var(--header);
    font-size: 24px;
}

.product-details-wrapper .product-image-items .nav {
    border: unset;
    display: flex;
    align-items: center;
    margin-top: 20px;
}

.product-details-wrapper .product-image-items .nav .nav-link {
    padding: 0;
    border: unset;
    background: none;
}

.product-details-wrapper .product-image-items .nav .nav-link .image-tab img {
    width: 100%;
    height: 100%;
}

.product-details-wrapper .product-details-content {
    margin-left: 60px;
}

@media (max-width: 1399px) {
    .product-details-wrapper .product-details-content {
        margin-left: 30px;
    }
}

@media (max-width: 991px) {
    .product-details-wrapper .product-details-content {
        margin-left: 0;
    }
}

.product-details-wrapper .product-details-content .star a {
    color: var(--theme3);
    font-size: 16px;
    font-weight: 600;
}

.product-details-wrapper .product-details-content .star span {
    background-color: var(--theme3);
    font-weight: 600;
    color: var(--header);
    padding: 5px 6px;
    border-radius: 3px;
    margin-right: 10px;
}

.product-details-wrapper .product-details-content .price-list {
    gap: 20px;
}

.product-details-wrapper .product-details-content .price-list span {
    font-weight: 600;
    font-size: 28px;
    color: var(--header);
}

.product-details-wrapper .product-details-content .price-list del {
    font-size: 20px;
    color: var(--theme2);
    font-weight: 500;
}

.product-details-wrapper .product-details-content .price-list h3 {
    font-size: 40px;
    color: var(--theme2);
}

.product-details-wrapper .product-details-content .cart-wrp {
    margin-top: 30px;
    margin-bottom: 30px;
}

.product-details-wrapper .product-details-content .cart-wrp .cart-quantity {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 40px;
}

.product-details-wrapper .product-details-content .cart-wrp .cart-quantity h5 {
    font-weight: 700;
}

.product-details-wrapper .product-details-content .cart-wrp .cart-quantity .quantity {
    width: 190px;
    border: 1px solid var(--header);
    border-radius: 6px;
}

.product-details-wrapper .product-details-content .cart-wrp .cart-quantity .quantity input {
    width: 60px;
    height: 55px;
    text-align: center;
    background-color: transparent;
    border: 1px solid var(--border2);
    color: var(--header);
}

.product-details-wrapper .product-details-content .cart-wrp .cart-quantity .quantity .minus {
    color: var(--header);
    font-size: 32px;
}

.product-details-wrapper .product-details-content .cart-wrp .cart-quantity .quantity .plus {
    color: var(--header);
    font-size: 32px;
}

.product-details-wrapper .product-details-content .cart-wrp .cart-quantity .quantity .qty {
    color: var(--header);
}

.product-details-wrapper .product-details-content .cart-wrp .shop-button {
    justify-content: space-between;
    gap: 30px;
}

.product-details-wrapper .product-details-content .cart-wrp .shop-button .theme-btn {
    width: 88%;
    border-radius: 30px;
}

.product-details-wrapper .product-details-content .cart-wrp .shop-button .star-icon {
    width: 60px;
    height: 60px;
    line-height: 60px;
    border-radius: 36px;
    background-color: var(--white);
    box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.08);
    font-size: 20px;
    display: inline-block;
    text-align: center;
    transition: all 0.4s ease-in-out;
}

.product-details-wrapper .product-details-content .cart-wrp .shop-button .star-icon i {
    color: var(--header);
    transition: all 0.4s ease-in-out;
}

.product-details-wrapper .product-details-content .cart-wrp .shop-button .star-icon:hover {
    background-color: var(--theme);
}

.product-details-wrapper .product-details-content .cart-wrp .shop-button .star-icon:hover i {
    color: var(--white);
}

.product-details-wrapper .product-details-content .shop-text {
    font-weight: 500;
    margin-bottom: 20px;
}

.product-details-wrapper .product-details-content .shop-text span {
    font-weight: 600;
}

.product-details-wrapper .product-details-content .details-info {
    position: relative;
}

.product-details-wrapper .product-details-content .details-info:not(:last-child) {
    margin-bottom: 10px;
}

.product-details-wrapper .product-details-content .details-info span {
    font-size: 18px;
    font-weight: 600;
    text-transform: uppercase;
    position: absolute;
    left: 0;
    top: 2px;
}

.product-details-wrapper .product-details-content .details-info a {
    font-size: 18px;
    font-weight: 600;
    color: var(--header);
    margin-left: 100px;
    text-transform: uppercase;
}

.product-details-wrapper .single-tab {
    padding-top: 80px;
}

@media (max-width: 575px) {
    .product-details-wrapper .single-tab {
        padding-top: 50px;
    }
}

.product-details-wrapper .single-tab .nav {
    border-bottom: 1px solid var(--border2);
    padding-bottom: 20px;
}

.product-details-wrapper .single-tab .nav .nav-link {
    text-align: center;
    padding: 20px 50px;
    background-color: transparent;
    border-radius: 5px;
    font-weight: 700;
    font-size: 18px;
    text-transform: uppercase;
    color: var(--header);
}

.product-details-wrapper .single-tab .nav .nav-link.active {
    position: relative;
    background-color: var(--theme3);
    text-align: center;
    padding: 20px 50px;
}

.product-details-wrapper .single-tab .nav .nav-link.active h6 {
    color: var(--header);
}

.product-details-wrapper .single-tab .description-items .description-content {
    margin-right: 50px;
}

@media (max-width: 1399px) {
    .product-details-wrapper .single-tab .description-items .description-content {
        margin-right: 30px;
    }
}

@media (max-width: 991px) {
    .product-details-wrapper .single-tab .description-items .description-content {
        margin-right: 0;
    }
}

.product-details-wrapper .single-tab .description-items .description-content h3 {
    margin-bottom: 15px;
    font-size: 28px;
}

.product-details-wrapper .single-tab .description-items .description-content .description-list-items {
    margin-top: 20px;
    gap: 200px;
}

@media (max-width: 575px) {
    .product-details-wrapper .single-tab .description-items .description-content .description-list-items {
        flex-wrap: wrap;
        gap: 30px;
    }
}

.product-details-wrapper .single-tab .description-items .description-content .description-list-items .description-list li {
    font-size: 16px;
    font-weight: 400;
    font-weight: 500;
}

.product-details-wrapper .single-tab .description-items .description-content .description-list-items .description-list li:not(:last-child) {
    margin-bottom: 15px;
}

.product-details-wrapper .single-tab .description-items .description-content .description-list-items .description-list li i {
    color: var(--theme);
    margin-right: 10px;
    font-weight: 600;
}

.product-details-wrapper .single-tab .description-items .description-content .description-list-items .description-list li span {
    color: var(--text);
    font-size: 20px;
    font-weight: 400;
}

@media (max-width: 767px) {
    .product-details-wrapper .single-tab .review-items .admin-items {
        justify-content: center;
        gap: 30px;
    }
}

.product-details-wrapper .single-tab .review-items .admin-items .admin-img {
    width: 100px;
    height: 100px;
}

.product-details-wrapper .single-tab .review-items .admin-items .admin-img img {
    border-radius: 50%;
}

.product-details-wrapper .single-tab .review-items .admin-items .content {
    position: relative;
    border: 1px solid var(--border);
}

.product-details-wrapper .single-tab .review-items .admin-items .content::before {
    position: absolute;
    content: "";
    width: 30px;
    height: 30px;
    background-color: var(--white);
    border-left: 1px solid var(--border);
    border-bottom: 1px solid var(--border);
    transform: rotate(45deg);
    top: 40%;
    left: -16px;
}

@media (max-width: 575px) {
    .product-details-wrapper .single-tab .review-items .admin-items .content::before {
        display: none;
    }
}

.product-details-wrapper .single-tab .review-items .admin-items .content .head-content h5 span {
    font-size: 16px;
    margin-left: 30px;
}

.product-details-wrapper .single-tab .review-items .admin-items .content .star i {
    font-size: 16px;
    color: #ff9200;
}

.product-details-wrapper .single-tab .review-title .rate-now {
    margin-top: 15px;
    gap: 15px;
}

.product-details-wrapper .single-tab .review-title .rate-now p {
    font-size: 16px;
    text-transform: uppercase;
}

.product-details-wrapper .single-tab .review-title .rate-now i {
    font-size: 16px;
    color: #ff9200;
}

.product-details-wrapper .single-tab .review-form {
    margin-top: 40px;
}

.product-details-wrapper .single-tab .review-form .form-clt input {
    width: 100%;
    outline: none;
    border: none;
    padding: 18px 35px;
    color: var(--text);
    background-color: rgb(245, 245, 245);
    font-size: 16px;
    font-weight: 500;
    text-transform: uppercase;
}

@media (max-width: 575px) {
    .product-details-wrapper .single-tab .review-form .form-clt input {
        padding: 15px 20px;
    }
}

.product-details-wrapper .single-tab .review-form .form-clt-big textarea {
    padding: 18px 35px 170px;
    width: 100%;
    outline: none;
    color: var(--text);
    background-color: rgb(245, 245, 245);
    font-size: 14px;
    border: none;
    text-transform: uppercase;
}

@media (max-width: 575px) {
    .product-details-wrapper .single-tab .review-form .form-clt-big textarea {
        padding: 15px 20px;
    }
}

.cta-section {
    margin-top: -100px;
    position: relative;
    z-index: 1;
}

.cta-wrapper .cta-left {
    padding: 40px 50px;
    background-color: var(--white);
    box-shadow: var(---box-shadow);
}

@media (max-width: 991px) {
    .cta-wrapper .cta-left {
        padding: 30px 25px;
    }
}

@media (max-width: 767px) {
    .cta-wrapper .cta-left {
        text-align: center;
        padding: 30px 30px;
    }
}

.cta-wrapper .cta-left h2 {
    font-size: 90px;
    color: var(--theme);
}

@media (max-width: 991px) {
    .cta-wrapper .cta-left h2 {
        font-size: 56px;
    }
}

@media (max-width: 767px) {
    .cta-wrapper .cta-left h2 {
        font-size: 48px;
    }
}

.cta-wrapper .cta-left p {
    font-size: 18px;
}

@media (max-width: 767px) {
    .cta-wrapper .cta-left p {
        font-size: 16px;
    }
}

.cta-wrapper .cta-right {
    box-shadow: var(---box-shadow);
    background-color: var(--white);
    padding: 60px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    z-index: 9;
    height: 100%;
    overflow: hidden;
}

@media (max-width: 991px) {
    .cta-wrapper .cta-right {
        padding: 45px 30px;
        height: initial;
    }
}

@media (max-width: 767px) {
    .cta-wrapper .cta-right {
        flex-direction: column;
        gap: 30px;
        padding: 50px 30px;
        justify-content: center;
        text-align: center;
    }
}

.cta-wrapper .cta-right .shape-image {
    background-image: url(../img/shape.webp);
    background-repeat: no-repeat;
    background-size: contain;
    background-position: top left;
    width: 100%;
    height: 100%;
    position: absolute;
    top: -2px;
    bottom: 0;
    left: 0;
    z-index: -1;
}

@media (max-width: 1199px) {
    .cta-wrapper .cta-right .shape-image {
        left: -150px;
    }
}

@media (max-width: 991px) {
    .cta-wrapper .cta-right .shape-image {
        left: -100px;
    }
}

@media (max-width: 767px) {
    .cta-wrapper .cta-right .shape-image {
        background-size: cover;
        left: 0;
    }
}

@media (max-width: 767px) {
    .cta-wrapper .cta-right .cta-content h3,
    .cta-wrapper .cta-right .cta-content span {
        color: var(--white);
    }
}

.cta-wrapper .cta-right .cta-content span {
    font-size: 18px;
    color: var(--white);
    font-weight: 500;
}

@media (max-width: 767px) {
    .cta-wrapper .cta-right .cta-content span span {
        color: var(--white);
    }
}

.cta-wrapper .cta-right .cta-content h3 {
    font-size: 26px;
    color: var(--white);
}

@media (max-width: 767px) {
    .cta-wrapper .cta-right .call-area {
        text-align: center;
    }
}

.cta-wrapper .cta-right .call-area p {
    font-size: 18px;
    margin-bottom: 5px;
    text-align: right;
}

@media (max-width: 767px) {
    .cta-wrapper .cta-right .call-area p {
        text-align: center;
        color: var(--white);
    }
}

.cta-wrapper .cta-right .call-area a {
    font-size: 26px;
    color: var(--header);
    font-weight: 700;
}

@media (max-width: 767px) {
    .cta-wrapper .cta-right .call-area a {
        color: var(--white);
    }
}

.cta-section-2 {
    position: relative;
    background-size: cover;
    background-repeat: no-repeat;
}

.cta-section-2::before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    content: "";
    background-color: rgb(13, 6, 44);
    opacity: 0.8;
}

.cta-section-2 .shape-image {
    position: absolute;
    top: 0;
    right: 0;
}

@media (max-width: 1199px) {
    .cta-section-2 .shape-image {
        display: none;
    }
}

.cta-section-2.style-2 {
    background-attachment: fixed;
}

.cta-section-2.style-2::before {
    opacity: 0.8;
}

.cta-section-2.style-2 .video-icon {
    margin-top: 50px;
    width: 140px;
    height: 140px;
    line-height: 140px;
    border-radius: 50%;
    background-color: transparent;
    border: 1px solid var(--white);
    display: inline-block;
    font-size: 22px;
    color: var(--theme);
}

@media (max-width: 575px) {
    .cta-section-2.style-2 .video-icon {
        width: 100px;
        height: 100px;
        line-height: 100px;
        margin-top: 30px;
    }
}

.cta-wrapper-2 {
    position: relative;
}

.cta-wrapper-2 .cta-content {
    margin: 0 auto;
    text-align: center;
}

.cta-wrapper-2 .cta-content h2 {
    color: var(--white);
    font-size: 60px;
}

@media (max-width: 1199px) {
    .cta-wrapper-2 .cta-content h2 {
        font-size: 52px;
    }
}

@media (max-width: 991px) {
    .cta-wrapper-2 .cta-content h2 {
        font-size: 48px;
    }
}

@media (max-width: 575px) {
    .cta-wrapper-2 .cta-content h2 {
        font-size: 34px;
    }
}

.cta-wrapper-3 {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    z-index: 9;
}

@media (max-width: 1199px) {
    .cta-wrapper-3 {
        flex-wrap: wrap;
        justify-content: center;
        gap: 30px;
        text-align: center;
    }
}

.video-section {
    position: relative;
    padding: 230px 0;
}

.video-section::before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    content: "";
    background-color: rgb(0, 5, 28);
    opacity: 0.8;
}

@media (max-width: 575px) {
    .video-section {
        padding: 130px 0;
    }
}

@media (max-width: 991px) {
    .video-section {
        padding: 200px 0;
    }
}

.video-box {
    display: block;
}

.video-box .video-btn {
    position: relative;
    display: inline-block;
    width: 140px;
    height: 140px;
    line-height: 150px;
    font-size: 24px;
    color: var(--white);
    background-color: rgba(255, 255, 255, 0.1490196078);
    border-radius: 50%;
}

@media (max-width: 575px) {
    .video-box .video-btn {
        width: 100px;
        height: 100px;
        line-height: 110px;
    }
}

.video-box .ripple::before,
.video-box .ripple::after {
    width: 140px;
    height: 140px;
}

@media (max-width: 575px) {
    .video-box .ripple::before,
    .video-box .ripple::after {
        width: 100px;
        height: 100px;
    }
}

.cta-wrapper-4 {
    display: flex;
    align-items: center;
    position: relative;
    z-index: 9;
}

@media (max-width: 1199px) {
    .cta-wrapper-4 {
        flex-wrap: wrap;
        gap: 30px;
        justify-content: center;
        text-align: center;
    }
}

.cta-wrapper-4 .cta-content h2 {
    font-size: 60px;
    color: var(--white);
}

@media (max-width: 1399px) {
    .cta-wrapper-4 .cta-content h2 {
        font-size: 52px;
    }
}

@media (max-width: 1199px) {
    .cta-wrapper-4 .cta-content h2 {
        font-size: 48px;
    }
}

@media (max-width: 991px) {
    .cta-wrapper-4 .cta-content h2 {
        font-size: 42px;
    }
}

@media (max-width: 767px) {
    .cta-wrapper-4 .cta-content h2 {
        font-size: 35px;
    }
}

@media (max-width: 575px) {
    .cta-wrapper-4 .cta-content h2 {
        font-size: 32px;
    }
}

.cta-image img {
    border-radius: 50px;
}

.cta-section-3 {
    position: relative;
    z-index: 1;
}

.cta-section-3::before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    content: "";
    background-image: var(---color-gradient-2);
    opacity: 0.8;
}

.marque-section {
    position: relative;
}

@media (max-width: 1199px) {
    .marque-section .line-area {
        display: none;
    }
}

.marque-section .line-area span {
    position: absolute;
    content: "";
    width: 1px;
    height: 100%;
    top: 0;
    left: 26%;
    background-color: #efefef;
    z-index: -1;
}

.marque-section .line-area span:nth-child(2) {
    left: 50%;
}

.marque-section .line-area span:nth-child(3) {
    left: 80%;
}

.marquee-wrapper {
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    margin-bottom: 45px;
    margin-top: -2px;
}

@media (max-width: 575px) {
    .marquee-wrapper {
        margin-top: -25px;
    }
}

.marquee-wrapper.style-2 {
    line-height: 40px;
}

@media (max-width: 575px) {
    .marquee-wrapper.style-2 {
        line-height: 75px;
    }
}

.marquee-wrapper.style-2 .text-slider {
    font-size: 24px;
    color: var(--header);
    margin-right: 40px;
}

.text-slider {
    font-size: 120px;
    height: 100px;
    line-height: 90px;
    font-weight: 700;
    text-transform: uppercase;
    margin-right: 20px;
    color: #e1e1de;
}

@media (max-width: 575px) {
    .text-slider {
        font-size: 50px;
        height: 70px;
        line-height: 65px;
    }
}

.marquee-inner {
    position: absolute;
    display: inline-flex;
    width: 200%;
}

.marquee-list {
    float: left;
    width: 50%;
}

.marquee-item {
    float: left;
    transition: animation 0.2s ease-out;
}

.marquee-inner.to-left {
    animation: marqueeLeft 25s linear infinite;
}

@keyframes marqueeLeft {
    0% {
        left: 0;
    }
    100% {
        left: -100%;
    }
}

.marquee-inner.to-right {
    animation: marqueeRight 25s linear infinite;
}

@keyframes marqueeRight {
    0% {
        right: 0;
    }
    100% {
        right: -100%;
    }
}

.testimonial-section {
    position: relative;
}

.testimonial-section .left-shape {
    position: absolute;
    bottom: 0;
    left: 0;
    opacity: 0.9;
    animation: top-image-bounce 3s infinite ease-in-out;
}

.testimonial-section .right-shape {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
}

.testimonial-card-items {
    background-color: var(--white);
    padding: 40px;
    position: relative;
    margin-top: 30px;
    display: inline-block;
}

.testimonial-card-items::before {
    position: absolute;
    bottom: 0;
    right: 0;
    content: "";
    background-image: url(/website/img/client/shape.webp);
    background-size: cover;
    background-repeat: no-repeat;
    width: 115px;
    height: 160px;
    filter: grayscale(100%);
    transition: all 0.4s ease-in-out;
    opacity: 0.2;
}

.testimonial-card-items .testimonial-image {
    width: 80px;
    height: 80px;
    border-radius: 50%;
}

.testimonial-card-items .icon {
    position: absolute;
    top: 40px;
    right: 40px;
    font-size: 48px;
    transition: all 0.4s ease-in-out;
}

.testimonial-card-items .testimonial-content {
    margin-top: 20px;
    position: relative;
    z-index: 9;
}

.testimonial-card-items .testimonial-content h4 {
    margin-top: 20px;
    margin-bottom: 5px;
}

.testimonial-card-items:hover::before {
    filter: initial;
    opacity: 1;
}

.testimonial-card-items:hover .icon {
    color: var(--theme);
}

.testimonial-card-items.active::before {
    filter: initial;
    opacity: 1;
}

.testimonial-card-items.active .icon {
    color: var(--theme);
}

.testimonial-section-2 {
    position: relative;
}

.testimonial-section-2 .shape-left {
    position: absolute;
    bottom: 0;
    left: 0;
}

@media (max-width: 1199px) {
    .testimonial-section-2 .shape-left {
        display: none;
    }
}

.testimonial-section-2 .right-shape {
    position: absolute;
    top: -37%;
    right: 0;
}

@media (max-width: 1199px) {
    .testimonial-section-2 .right-shape {
        display: none;
    }
}

.testimonial-wrapper {
    position: relative;
    margin-bottom: 50px;
}

@media (max-width: 575px) {
    .testimonial-wrapper {
        text-align: center;
        margin: 0 auto;
    }
}

.testimonial-wrapper .testimonial-items {
    max-width: 580px;
}

@media (max-width: 575px) {
    .testimonial-wrapper .testimonial-items {
        text-align: center;
        margin: 0 auto;
    }
}

.testimonial-wrapper .testimonial-items .client-image {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin-bottom: 20px;
}

@media (max-width: 575px) {
    .testimonial-wrapper .testimonial-items .client-image {
        text-align: center;
        margin: 0 auto 20px;
    }
}

.testimonial-wrapper .testimonial-items .testiminial-content p {
    margin-bottom: 30px;
}

@media (max-width: 575px) {
    .testimonial-wrapper .testimonial-items .testiminial-content p {
        margin-bottom: 20px;
    }
}

.testimonial-wrapper .array-button {
    gap: 30px;
    display: flex;
    align-items: center;
    justify-content: end;
}

@media (max-width: 575px) {
    .testimonial-wrapper .array-button {
        justify-content: center;
    }
}

.testimonial-section-3 {
    position: relative;
}

.testimonial-section-3 .shape-image {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
}

.testimonial-right {
    width: 860px;
}

@media (max-width: 1600px) {
    .testimonial-right {
        width: initial;
        max-width: 860px;
    }
}

.testimonial-right .testimonial-card-items-2 .testimonial-content {
    background-color: var(--white);
    box-shadow: var(---box-shadow);
    padding: 40px;
    position: relative;
    border-bottom: 4px solid var(--theme);
}

.testimonial-right .testimonial-card-items-2 .testimonial-content::before {
    position: absolute;
    top: 0;
    right: 0;
    content: "";
    background-image: url(/website/img/client/shape-2.webp);
    background-size: cover;
    background-repeat: no-repeat;
    width: 73px;
    height: 101px;
    filter: grayscale(100%);
    transition: all 0.4s ease-in-out;
    opacity: 0.2;
}

.testimonial-right .testimonial-card-items-2 .client-info {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-top: 30px;
}

.testimonial-right .testimonial-card-items-2 .client-info .client-image {
    width: 80px;
    height: 80px;
    border-radius: 50%;
}

.testimonial-right .testimonial-card-items-2 .client-info .client-content h4 {
    margin-bottom: 5px;
}

.testimonial-right .testimonial-card-items-2:hover .testimonial-content::before {
    filter: initial;
    opacity: 1;
}

.testimonial-right .testimonial-card-items-2.active .testimonial-content::before {
    filter: initial;
    opacity: 1;
}

.brand-wrapper .brand-image {
    margin: 0 auto;
    text-align: center;
    padding: 30px 10px;
    background-color: var(--bg);
    filter: grayscale(100%);
    transition: all 0.4s ease-in-out;
}

.brand-wrapper .brand-image:hover {
    filter: initial;
}

.brand-wrapper.style-2 .brand-image {
    background-color: var(--white);
    box-shadow: var(---box-shadow);
}

.brand-wrapper.style-3 {
    margin-bottom: 55px;
}

@media (max-width: 1399px) {
    .brand-wrapper.style-3 {
        margin-bottom: 30px;
    }
}

@media (max-width: 1199px) {
    .brand-wrapper.style-3 {
        margin-bottom: 0;
    }
}

.brand-wrapper.style-3 .brand-image {
    background-color: transparent;
    box-shadow: none;
    padding: 0;
}

.brand-wrapper .swiper-slide.swiper-slide-active .brand-image {
    filter: initial;
}

.brand-section-2 {
    position: relative;
}

.brand-section-2::before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    content: "";
    background-color: var(--bg);
    top: 20%;
}

.brand-section-3 {
    background-color: #1c1b23;
    position: relative;
}

.brand-section-3 .layer-shape {
    position: absolute;
    top: 0;
    left: 0;
}

.pricing-section {
    position: relative;
}

.pricing-section .shape-image {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: -1;
}

.pricing-section .shape-image img {
    width: 100%;
    height: 100%;
}

@media (max-width: 1399px) {
    .pricing-section .shape-image {
        display: none;
    }
}

.single-pricing-items {
    margin-top: 30px;
    background-color: var(--bg);
    padding: 15px;
    border: 1px solid var(--border);
    overflow: hidden;
    position: relative;
    transition: all 0.4s ease-in-out;
}

.single-pricing-items .pricing-header {
    padding: 45px;
    background-color: var(--white);
    position: relative;
    z-index: 9;
}

@media (max-width: 575px) {
    .single-pricing-items .pricing-header {
        padding: 40px;
    }
}

.single-pricing-items .pricing-header h2 {
    margin-bottom: 10px;
}

.single-pricing-items .pricing-header h2 sub {
    font-size: 18px;
    margin-left: -10px;
}

.single-pricing-items .pricing-header span {
    font-size: 18px;
    font-weight: 500;
    color: var(--header);
}

.single-pricing-items .pricing-header .icon-area {
    position: relative;
    margin-top: 40px;
}

.single-pricing-items .pricing-header .icon-area img {
    transition: all 500ms ease;
}

@media (max-width: 575px) {
    .single-pricing-items .pricing-header .icon-area {
        margin-top: 20px;
    }
}

.single-pricing-items .pricing-header .icon-area .content {
    background-color: var(--white);
    box-shadow: var(---box-shadow);
    text-align: center;
    padding: 5px 15px;
    position: absolute;
    right: 0;
    bottom: 0;
}

.single-pricing-items ul {
    padding: 40px 45px 50px;
    position: relative;
    z-index: 9;
}

@media (max-width: 575px) {
    .single-pricing-items ul {
        padding: 40px 40px 30px;
    }
}

.single-pricing-items ul li {
    position: relative;
}

.single-pricing-items ul li:not(:last-child) {
    margin-bottom: 10px;
}

.single-pricing-items ul li i {
    color: var(--theme);
    margin-right: 5px;
}

.single-pricing-items ul .last-list {
    opacity: 0;
}

@media (max-width: 1399px) {
    .single-pricing-items ul .last-list {
        opacity: 1;
    }
}

.single-pricing-items .pricing-button {
    padding: 0 45px 50px;
    position: relative;
    z-index: 9;
}

@media (max-width: 575px) {
    .single-pricing-items .pricing-button {
        padding: 0 40px 40px;
    }
}

.single-pricing-items.active {
    border: 1px solid var(--theme);
}

.single-pricing-items .shape-image {
    position: absolute;
    right: 0;
    bottom: 0;
    top: 0;
    z-index: 1;
}

.single-pricing-items .shape-image img {
    width: 100%;
    height: 100%;
}

.single-pricing-items:hover {
    transform: translateY(-15px);
}

.single-pricing-items:hover .pricing-header .icon-area img {
    transform: scaleX(-1);
}

.news-section {
    position: relative;
}

.news-section .swiper-slide.swiper-slide-active .single-news-items .news-image .icon {
    transform: scale(1) translate(-50%, -50%);
    transition-delay: 300ms;
    opacity: 1;
    visibility: visible;
}

.news-section .swiper-slide.swiper-slide-active .single-news-items .news-image::after {
    opacity: 0.6;
    visibility: visible;
    transform: scale(1);
}

@media (max-width: 1199px) {
    .news-section .line-area {
        display: none;
    }
}

.news-section .line-area span {
    position: absolute;
    content: "";
    width: 1px;
    height: 100%;
    top: 0;
    left: 26%;
    background-color: #efefef;
    z-index: -1;
}

.news-section .line-area span:nth-child(2) {
    left: 50%;
}

.news-section .line-area span:nth-child(3) {
    left: 80%;
}

.news-section.style-2 {
    position: relative;
}

.news-section.style-2::before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    content: "";
    background-color: var(--bg);
    height: 50%;
}

.news-section.style-2 .shape-image {
    position: absolute;
    top: 0;
    left: 0;
}

.single-news-items {
    margin-top: 30px;
    overflow: hidden;
}

.single-news-items .news-image {
    height: 280px;
    position: relative;
    transition: all 0.4s ease-in-out;
}

.single-news-items .news-image .icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 42px;
    z-index: 9;
    transition: all 300ms ease;
    transition-delay: 0s;
    transition-delay: 0s;
    z-index: 3;
    visibility: hidden;
    opacity: 0;
}

.single-news-items .news-image .icon i {
    color: var(--white);
}

.single-news-items .news-image::after {
    position: absolute;
    content: "";
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    opacity: 0;
    visibility: hidden;
    transform: scale(0.5);
    transition: 0.5s all ease;
    background-color: rgb(0, 5, 28);
}

.single-news-items .news-content {
    padding-top: 25px;
}

@media (max-width: 575px) {
    .single-news-items .news-content {
        padding-top: 15px;
    }
}

.single-news-items .news-content h3 {
    line-height: 1.3;
    margin-bottom: 10px;
}

@media (max-width: 991px) {
    .single-news-items .news-content h3 {
        font-size: 22px;
    }
}

@media (max-width: 575px) {
    .single-news-items .news-content h3 {
        font-size: 20px;
    }
}

.single-news-items .news-content h3 a:hover {
    color: var(--theme);
}

.single-news-items:hover .news-image .icon {
    transform: scale(1) translate(-50%, -50%);
    transition-delay: 300ms;
    opacity: 1;
    visibility: visible;
}

.single-news-items:hover .news-image::after {
    opacity: 0.6;
    visibility: visible;
    transform: scale(1);
}

.single-news-items-2 {
    margin-top: 30px;
}

.single-news-items-2 .news-image {
    height: 480px;
    position: relative;
}

@media (max-width: 575px) {
    .single-news-items-2 .news-image {
        height: 400px;
    }
}

.single-news-items-2 .news-image::before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    content: "";
    background: linear-gradient(to bottom, rgba(99, 92, 92, 0) 0%, #292930 100%);
}

.single-news-items-2 .news-image .post-date {
    position: absolute;
    top: 40px;
    left: 40px;
    background-color: var(--white);
    padding: 10px 24px;
}

.single-news-items-2 .news-image .post-date span {
    color: var(--header);
    font-weight: 600;
    font-size: 18px;
}

.single-news-items-2 .news-image .news-content {
    padding: 40px;
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 9;
}

.single-news-items-2 .news-image .news-content .icon {
    margin-bottom: 20px;
}

.single-news-items-2 .news-image .news-content .icon span {
    color: var(--white);
}

.single-news-items-2 .news-image .news-content .icon i {
    color: var(--white);
    margin-right: 5px;
}

.single-news-items-2 .news-image .news-content h4 a {
    color: var(--white);
}

.single-news-items-2 .news-image .news-content h4 a:hover {
    color: var(--theme);
}

.single-news-items-2 .news-image .news-content .post-list {
    background-color: rgba(255, 255, 255, 0.0784313725);
    padding: 18px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 20px;
    transition: all 0.4s ease-in-out;
}

@media (max-width: 575px) {
    .single-news-items-2 .news-image .news-content .post-list {
        padding: 14px 20px;
    }
}

.single-news-items-2 .news-image .news-content .post-list li {
    color: var(--white);
}

.single-news-items-2 .news-image .news-content .post-list li span {
    margin-left: 5px;
}

.single-news-items-2 .news-image .news-content .post-list li a i {
    color: var(--white);
    font-size: 18px;
    transition: all 0.4s ease-in-out;
}

.single-news-items-2:hover .news-image .news-content .post-list {
    background-color: var(--theme);
}

.single-news-items-2.active .news-image .news-content .post-list {
    background-color: var(--theme);
}

.single-news-items-3 {
    margin-top: 30px;
}

.single-news-items-3 .news-image {
    height: 240px;
    position: relative;
}

.single-news-items-3 .news-image .post-date {
    position: absolute;
    left: 60px;
    bottom: 50px;
    color: var(--white);
    z-index: 1;
}

.single-news-items-3 .news-image::before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    content: "";
    background-color: rgb(3, 5, 26);
    transform: scale(1, 0);
    transition: transform 500ms ease;
    transform-origin: bottom center;
    opacity: 0.6;
}

.single-news-items-3 .news-content {
    padding: 30px;
    background-color: var(--white);
    box-shadow: var(---box-shadow);
    margin-left: 30px;
    margin-top: -30px;
    position: relative;
    z-index: 9;
    overflow: hidden;
}

@media (max-width: 575px) {
    .single-news-items-3 .news-content {
        margin-left: 0;
    }
}

.single-news-items-3 .news-content span {
    margin-bottom: 10px;
    display: inline-block;
    transition: all 0.4s ease-in-out;
}

.single-news-items-3 .news-content h3 {
    font-size: 26px;
}

.single-news-items-3 .news-content .news-btn {
    margin-top: 30px;
    position: relative;
    z-index: 9;
}

.single-news-items-3 .news-content .news-btn a {
    display: inline-block;
    font-weight: 600;
    text-transform: uppercase;
}

.single-news-items-3 .news-content::before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    content: "";
    background-color: var(--theme);
    transform: scale(1, 0);
    transition: transform 500ms ease;
    transform-origin: top center;
    z-index: -1;
}

.single-news-items-3 .news-content::after {
    position: absolute;
    bottom: 0;
    right: -70px;
    content: "";
    width: 140px;
    height: 125px;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url(/website/img/news/shape.webp);
    opacity: 0;
    visibility: hidden;
    transition: all 0.4s ease-in-out;
    z-index: 1;
}

.single-news-items-3:hover .news-image::before {
    transform: scale(1, 1);
    transform-origin: top center;
}

.single-news-items-3:hover .news-content::before {
    transform: scale(1, 1);
    transform-origin: bottom center;
}

.single-news-items-3:hover .news-content::after {
    opacity: 1;
    visibility: visible;
    right: 0;
}

.single-news-items-3:hover .news-content span {
    color: var(--white);
}

.single-news-items-3:hover .news-content h3 a {
    color: var(--white);
}

.single-news-items-3:hover .news-content .news-btn a {
    color: var(--white);
}

.single-news-items-3.active .news-image::before {
    transform: scale(1, 1);
    transform-origin: top center;
}

.single-news-items-3.active .news-content::before {
    transform: scale(1, 1);
    transform-origin: bottom center;
}

.single-news-items-3.active .news-content::after {
    right: 0;
    opacity: 1;
    visibility: visible;
}

.single-news-items-3.active .news-content span {
    color: var(--white);
}

.single-news-items-3.active .news-content h3 a {
    color: var(--white);
}

.single-news-items-3.active .news-content .news-btn a {
    color: var(--white);
}

.blog-wrapper h1,
.blog-wrapper h2,
.blog-wrapper h3,
.blog-wrapper h4,
.blog-wrapper h5,
.blog-wrapper h6 {
    font-weight: 600;
}

.blog-wrapper .single-blog-post {
    margin-bottom: 40px;
    position: relative;
}

.blog-wrapper .single-blog-post .video__button {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    z-index: 2;
}

.blog-wrapper .single-blog-post .video__button .video__btn__wrp {
    margin-bottom: 0;
}

.blog-wrapper .single-blog-post.post-details.format-quote .post-content {
    position: relative;
    background: transparent;
    border: none;
}

.blog-wrapper .single-blog-post.post-details .post-content {
    border: none;
}

.blog-wrapper .single-blog-post.post-details .post-content img {
    margin-top: 20px;
    margin-bottom: 20px;
}

.blog-wrapper .single-blog-post.post-details .post-content .single-post-image {
    width: 100%;
    height: 100%;
}

.blog-wrapper .single-blog-post.post-details .post-content h1 {
    font-size: 36px;
    line-height: 1.4;
}

.blog-wrapper .single-blog-post.post-details .post-content h1,
.blog-wrapper .single-blog-post.post-details .post-content h2,
.blog-wrapper .single-blog-post.post-details .post-content h3,
.blog-wrapper .single-blog-post.post-details .post-content h4,
.blog-wrapper .single-blog-post.post-details .post-content h5,
.blog-wrapper .single-blog-post.post-details .post-content h6 {
    margin-top: 10px;
}

.blog-wrapper .single-blog-post.post-details .post-content table {
    margin-top: 20px;
}

@media (max-width: 767px) {
    .blog-wrapper .single-blog-post.post-details .post-content h1 {
        font-size: 28px;
    }
    .blog-wrapper .single-blog-post.post-details .post-content h3 {
        font-size: 20px;
    }
}

.blog-wrapper .single-blog-post.post-details blockquote,
.blog-wrapper .single-blog-post.post-details .wp-block-quote {
    background-color: var(--theme);
    color: var(--white);
    font-size: 26px;
    line-height: 1.4;
    font-family: "Kanit", sans-serif;
    font-weight: 400;
    padding: 60px;
    text-align: center;
    margin: 40px 0px;
    position: relative;
    z-index: 1;
}

.blog-wrapper .single-blog-post.post-details blockquote a,
.blog-wrapper .single-blog-post.post-details .wp-block-quote a {
    color: var(--white);
}

.blog-wrapper .single-blog-post.post-details blockquote a:hover,
.blog-wrapper .single-blog-post.post-details .wp-block-quote a:hover {
    color: var(--text);
}

@media (max-width: 767px) {
    .blog-wrapper .single-blog-post.post-details blockquote,
    .blog-wrapper .single-blog-post.post-details .wp-block-quote {
        padding: 30px 15px;
        font-size: 18px;
        line-height: 1.5;
    }
}

.blog-wrapper .single-blog-post.post-details blockquote::before,
.blog-wrapper .single-blog-post.post-details .wp-block-quote::before {
    right: 30px;
    font-size: 110px;
    line-height: 1;
    font-family: "Font Awesome 5 Pro";
    position: absolute;
    content: "\f10d";
    bottom: -20px;
    color: var(--white);
    z-index: -1;
    opacity: 0.1;
    font-weight: 900;
}

.blog-wrapper .single-blog-post.format-video .post-featured-thumb,
.blog-wrapper .single-blog-post.category-video-post .post-featured-thumb {
    z-index: 1;
    position: relative;
}

.blog-wrapper .single-blog-post.format-video .post-featured-thumb::before,
.blog-wrapper .single-blog-post.category-video-post .post-featured-thumb::before {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    content: "";
    background-color: #282835;
    opacity: 0.3;
    z-index: -1;
}

.blog-wrapper .single-blog-post.quote-post .post-content,
.blog-wrapper .single-blog-post.format-quote .post-content {
    position: relative;
    background-color: var(--theme);
    border: none;
}

.blog-wrapper .single-blog-post.quote-post .post-content::before,
.blog-wrapper .single-blog-post.format-quote .post-content::before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    content: "";
    background-image: url(/website/img/quotepost.webp);
    background-repeat: no-repeat;
    background-size: cover;
}

.blog-wrapper .single-blog-post.quote-post .quote-content,
.blog-wrapper .single-blog-post.format-quote .quote-content {
    overflow: hidden;
    padding: 50px;
}

@media (max-width: 585px) {
    .blog-wrapper .single-blog-post.quote-post .quote-content,
    .blog-wrapper .single-blog-post.format-quote .quote-content {
        text-align: center;
        font-size: 24px;
        padding: 30px;
    }
}

.blog-wrapper .single-blog-post.quote-post .quote-content .icon,
.blog-wrapper .single-blog-post.format-quote .quote-content .icon {
    font-size: 70px;
    line-height: 80px;
    float: left;
    overflow: hidden;
    margin-right: 30px;
}

@media (max-width: 585px) {
    .blog-wrapper .single-blog-post.quote-post .quote-content .icon,
    .blog-wrapper .single-blog-post.format-quote .quote-content .icon {
        float: none;
        margin-bottom: 10px;
        margin-right: 0;
        font-size: 50px;
        line-height: 60px;
    }
}

.blog-wrapper .single-blog-post.quote-post .quote-content .quote-text,
.blog-wrapper .single-blog-post.format-quote .quote-content .quote-text {
    overflow: hidden;
}

.blog-wrapper .single-blog-post.quote-post .quote-content h2,
.blog-wrapper .single-blog-post.format-quote .quote-content h2 {
    margin-top: -2px;
    font-size: 30px;
    color: var(--white);
}

.blog-wrapper .single-blog-post.quote-post .quote-content .post-meta,
.blog-wrapper .single-blog-post.format-quote .quote-content .post-meta {
    margin-top: 10px;
}

.blog-wrapper .single-blog-post.quote-post .quote-content .post-meta span,
.blog-wrapper .single-blog-post.quote-post .quote-content .post-meta i,
.blog-wrapper .single-blog-post.format-quote .quote-content .post-meta span,
.blog-wrapper .single-blog-post.format-quote .quote-content .post-meta i {
    color: var(--white);
}

.blog-wrapper .single-blog-post:last-child {
    margin-bottom: 0;
}

.blog-wrapper .single-blog-post .post-featured-thumb {
    height: 450px;
    width: 100%;
    position: relative;
    margin-bottom: -2px;
}

@media (max-width: 991px) {
    .blog-wrapper .single-blog-post .post-featured-thumb {
        height: 280px;
    }
}

@media (max-width: 767px) {
    .blog-wrapper .single-blog-post .post-featured-thumb {
        height: 230px;
    }
}

.blog-wrapper .single-blog-post .post-featured-thumb .video-play-btn {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    z-index: 2;
}

.blog-wrapper .single-blog-post .post-featured-thumb .video-play-btn .play-video {
    border-radius: 50%;
    width: 80px;
    height: 80px;
    line-height: 80px;
    font-size: 18px;
}

.blog-wrapper .single-blog-post .post-content {
    padding-top: 30px;
    overflow: hidden;
    box-sizing: border-box;
}

@media (max-width: 575px) {
    .blog-wrapper .single-blog-post .post-content {
        padding-top: 20px;
    }
}

.blog-wrapper .single-blog-post .post-content h2 {
    line-height: 1.4;
    font-size: 36px;
    font-weight: 600;
}

@media (max-width: 575px) {
    .blog-wrapper .single-blog-post .post-content h2 {
        font-size: 28px;
    }
}

.blog-wrapper .single-blog-post .post-content h2 a:hover {
    color: var(--theme);
}

.blog-wrapper .single-blog-post .post-content p {
    font-size: 16px;
}

@media (max-width: 767px) {
    .blog-wrapper .single-blog-post .post-content {
        padding: 30px 10px;
    }
}

@media (max-width: 414px) {
    .blog-wrapper .single-blog-post .post-content h2 {
        font-size: 22px;
    }
}

.blog-wrapper .single-blog-post .post-content .post-cat a {
    color: var(--white);
    display: inline-block;
    background-color: var(--theme);
    padding: 14px 18px 12px;
    line-height: 1;
    font-size: 16px;
    font-weight: 700;
    transition: all 0.3s ease-in-out;
    margin-bottom: 20px;
    text-transform: capitalize;
    font-family: "Kanit", sans-serif;
}

@media (max-width: 767px) {
    .blog-wrapper .single-blog-post .post-content .post-cat a {
        font-size: 12px;
    }
}

.blog-wrapper .single-blog-post .post-content .post-cat a:hover {
    background-color: var(--header);
}

.blog-wrapper .single-blog-post .post-content ul {
    margin-bottom: 0;
    list-style-type: disc;
    margin-top: 30px;
}

.blog-wrapper .single-blog-post .post-content ul ol {
    margin-bottom: 0;
}

.blog-wrapper .single-blog-post .post-content ul ul {
    margin-bottom: 0;
}

.blog-wrapper .single-blog-post .post-content ul li {
    line-height: 1.5;
    margin-top: 10px;
    list-style-type: disc;
    color: var(--text);
    position: relative;
    font-size: 18px;
    padding-left: 30px;
}

.blog-wrapper .single-blog-post .post-content ul li::before {
    position: absolute;
    left: 0;
    top: 0;
    font-family: "Font Awesome 5 Pro";
    content: "\f00c";
    color: var(--theme);
}

.blog-wrapper .single-blog-post .post-content ol {
    margin-bottom: 0;
    list-style-type: decimal;
}

.blog-wrapper .single-blog-post .post-content ol ul {
    margin-bottom: 0;
}

.blog-wrapper .single-blog-post .post-content ol ol {
    margin-bottom: 0;
}

.blog-wrapper .single-blog-post .post-content ol li {
    line-height: 1.5;
    margin-top: 10px;
    list-style-type: decimal;
}

.blog-wrapper .single-blog-post .post-content p {
    margin-top: 15px;
}

.blog-wrapper .single-blog-post .post-content .post-meta {
    margin-bottom: 10px;
}

.blog-wrapper .single-blog-post .post-content .post-meta span {
    color: var(--text);
    font-weight: 500;
    font-size: 18px;
    line-height: 1;
    display: inline-block;
    margin-right: 25px;
}

@media (max-width: 767px) {
    .blog-wrapper .single-blog-post .post-content .post-meta span {
        font-size: 15px;
    }
}

.blog-wrapper .single-blog-post .post-content .post-meta span i {
    margin-right: 5px;
    color: var(--theme);
    font-weight: 700;
}

.blog-wrapper .single-blog-post .post-content .author-info {
    overflow: hidden;
}

.blog-wrapper .single-blog-post .post-content .author-info .author-img {
    height: 40px;
    width: 40px;
    line-height: 40px;
    border-radius: 50%;
    background-color: #f2f2f2;
    margin-right: 15px;
    float: left;
    overflow: hidden;
    background-position: center;
    background-size: cover;
}

@media (max-width: 767px) {
    .blog-wrapper .single-blog-post .post-content .author-info .author-img {
        margin-right: 5px;
    }
}

.blog-wrapper .single-blog-post .post-content .author-info h5 {
    display: inline-block;
    line-height: 1;
    font-size: 16px;
    margin-top: 9px;
}

.blog-wrapper .single-blog-post .post-content .author-info h5 a:hover {
    color: var(--theme);
}

@media (max-width: 767px) {
    .blog-wrapper .single-blog-post .post-content .author-info h5 {
        font-size: 14px;
    }
}

.blog-wrapper .single-blog-post .post-content .post-link a {
    font-weight: 700;
}

.blog-wrapper .single-blog-post .post-content .post-link a:hover {
    color: var(--theme);
}

@media (max-width: 767px) {
    .blog-wrapper .single-blog-post .post-content .post-link a {
        font-size: 14px;
    }
}

.blog-wrapper .single-blog-post .post-content .post-link a i {
    margin-right: 5px;
}

@media (max-width: 767px) {
    .blog-wrapper .single-blog-post .post-content .post-link a i {
        margin-right: 3px;
    }
}

@media (min-width: 991px) {
    .news-area .main-sidebar {
        margin-left: 20px;
    }
}

@media (max-width: 991px) {
    .news-area .main-sidebar {
        margin-top: 40px;
    }
}

.news-area .main-sidebar .single-sidebar-widget {
    border: 2px solid #ededed;
    box-sizing: border-box;
    padding: 30px;
    margin-bottom: 40px;
}

.news-area .main-sidebar .single-sidebar-widget ul {
    padding-left: 0;
}

.news-area .main-sidebar .single-sidebar-widget ul>li {
    margin-bottom: 10px;
}

.news-area .main-sidebar .single-sidebar-widget ul>li a {
    font-size: 18px;
    font-weight: 600;
    text-transform: capitalize;
}

.news-area .main-sidebar .single-sidebar-widget ul>li a:hover {
    color: var(--theme);
}

.news-area .main-sidebar .single-sidebar-widget ul ul {
    padding-left: 20px;
}

.news-area .main-sidebar .single-sidebar-widget ul ul li {
    margin-top: 5px;
}

.news-area .main-sidebar .single-sidebar-widget:last-child {
    margin-bottom: 0;
}

.news-area .main-sidebar .single-sidebar-widget .wid-title {
    margin-bottom: 25px;
    padding-left: 45px;
    position: relative;
}

.news-area .main-sidebar .single-sidebar-widget .wid-title::before {
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    content: "";
    height: 4px;
    width: 18px;
    border-radius: 5px;
    position: absolute;
    background-color: var(--theme);
}

.news-area .main-sidebar .single-sidebar-widget .wid-title::after {
    left: 22px;
    top: 50%;
    transform: translateY(-50%);
    content: "";
    height: 4px;
    width: 4px;
    border-radius: 50%;
    position: absolute;
    background-color: var(--theme);
}

.news-area .main-sidebar .single-sidebar-widget .wid-title h4 {
    font-size: 24px;
    font-weight: 600;
}

.news-area .main-sidebar .single-sidebar-widget .social-link a {
    display: inline-block;
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    border-radius: 50%;
    background-color: var(--bg);
    font-size: 14px;
    margin-right: 5px;
    margin-bottom: 5px;
}

.news-area .main-sidebar .single-sidebar-widget .social-link a:hover {
    background-color: var(--theme);
    color: var(--white);
}

.search_widget form {
    width: 100%;
    position: relative;
}

.search_widget form input {
    background-color: var(--bg);
    font-size: 15px;
    padding: 20px;
    width: 100%;
    border: none;
    text-transform: capitalize;
    color: var(--header);
}

.search_widget form button {
    position: absolute;
    right: 0;
    top: 0;
    width: 70px;
    font-size: 18px;
    height: 100%;
    background-color: var(--theme);
    color: var(--white);
    text-align: center;
    transition: all 0.3s ease-in-out;
}

.search_widget form button:hover {
    background-color: var(--header);
}

.popular-posts .single-post-item,
.popular_posts .single-post-item {
    overflow: hidden;
    padding-bottom: 20px;
    margin-bottom: 20px;
    border-bottom: 1px solid #f2f2f2;
}

.popular-posts .single-post-item:last-child,
.popular_posts .single-post-item:last-child {
    margin-bottom: 0;
    border: none;
    padding-bottom: 0;
}

.popular-posts .single-post-item .thumb,
.popular_posts .single-post-item .thumb {
    height: 95px;
    width: 95px;
    background-color: #f2f2f2;
    float: left;
    overflow: hidden;
    margin-right: 20px;
}

.popular-posts .single-post-item .post-content,
.popular_posts .single-post-item .post-content {
    overflow: hidden;
}

.popular-posts .single-post-item .post-content h5,
.popular_posts .single-post-item .post-content h5 {
    font-size: 16px;
    line-height: 22px;
    font-weight: 600;
    margin-bottom: 0;
    text-transform: capitalize;
}

.popular-posts .single-post-item .post-content h5 a:hover,
.popular_posts .single-post-item .post-content h5 a:hover {
    color: var(--theme);
}

.popular-posts .single-post-item .post-content .post-date,
.popular_posts .single-post-item .post-content .post-date {
    margin-top: 10px;
    color: var(--theme);
    font-weight: 600;
    font-size: 15px;
    text-transform: capitalize;
}

.popular-posts .single-post-item .post-content .post-date i,
.popular_posts .single-post-item .post-content .post-date i {
    margin-right: 7px;
}

.widget_categories ul li {
    display: block;
}

.widget_categories ul li:last-child a {
    margin-bottom: 0;
}

.widget_categories ul li a {
    position: relative;
    background: var(--bg);
    padding: 17px 20px;
    line-height: 1;
    font-size: 14px;
    display: block;
    margin-bottom: 12px;
    font-weight: 500;
    transition: all 0.4s ease-in-out;
}

.widget_categories ul li a:hover {
    color: var(--theme);
}

.widget_categories ul li a i {
    margin-right: 10px;
}

.widget_categories ul li a span {
    position: absolute;
    width: 60px;
    line-height: 55px;
    height: 100%;
    content: "";
    background-color: var(--theme);
    right: 0;
    top: 0;
    text-align: center;
    color: var(--white);
}

.tagcloud a {
    display: inline-block;
    padding: 11px 26px;
    line-height: 1;
    font-size: 15px;
    font-weight: 600;
    background: var(--bg);
    margin-right: 10px;
    text-transform: capitalize;
    margin-bottom: 10px;
}

.tagcloud a:last-child {
    margin-right: 0;
}

.tagcloud a:hover {
    background-color: var(--theme);
    color: var(--white);
}

.widget_categories ul li {
    display: block;
    margin-bottom: 10px;
}

p.sticky-label {
    position: absolute;
    right: 0;
    background: var(--theme);
    color: var(--white);
    padding: 10px 40px;
    z-index: -1;
    top: 0;
    font-weight: 700;
}

.page-nav-wrap ul li {
    display: inline-block;
}

.page-nav-wrap ul li .page-numbers {
    display: inline-block;
    width: 60px;
    height: 60px;
    line-height: 60px;
    border-radius: 50%;
    background: #f6f6f6;
    font-weight: 700;
    transition: all 0.3s ease-in-out;
    margin: 0 2px;
}

.page-nav-wrap ul li .page-numbers.current {
    background-color: var(--theme);
    color: var(--white);
}

@media (max-width: 767px) {
    .page-nav-wrap ul li .page-numbers {
        margin-top: 10px;
        width: 50px;
        height: 50px;
        line-height: 50px;
        font-size: 14px;
    }
}

.page-nav-wrap ul li .page-numbers i {
    margin-top: 2px;
}

.page-nav-wrap ul li .page-numbers:hover {
    background-color: var(--theme);
    color: var(--white);
}

.social-share a {
    margin-left: 10px;
    transition: all 0.4s ease-in-out;
}

.social-share a:hover {
    color: var(--theme);
}

.tag-share-wrap {
    padding-bottom: 50px;
}

.tag-share-wrap h4 {
    margin-bottom: 25px;
}

@media (max-width: 767px) {
    .tag-share-wrap h4 {
        margin-bottom: 10px;
        font-size: 18px;
    }
}

@media (max-width: 767px) {
    .tag-share-wrap .tagcloud {
        margin-bottom: 20px;
    }
}

.comments-section-wrap {
    overflow: hidden;
}

.comments-heading h3,
.related-post-wrap h3,
.comment-form-wrap h3 {
    font-size: 26px;
}

@media (max-width: 767px) {
    .comments-heading h3,
    .related-post-wrap h3,
    .comment-form-wrap h3 {
        font-size: 20px;
    }
}

.comments-item-list .single-comment-item {
    margin-top: 30px;
}

.comments-item-list .single-comment-item ul {
    margin-bottom: 15px;
}

.comments-item-list .single-comment-item p,
.comments-item-list .single-comment-item span {
    font-size: 16px;
}

.comments-item-list .single-comment-item .author-img {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    float: left;
    overflow: hidden;
    margin-right: 30px;
}

@media (max-width: 991px) {
    .comments-item-list .single-comment-item .author-img {
        width: 80px;
        height: 80px;
        margin-right: 20px;
    }
}

@media (max-width: 767px) {
    .comments-item-list .single-comment-item .author-img {
        width: 70px;
        height: 70px;
        margin-right: 15px;
    }
}

@media (max-width: 585px) {
    .comments-item-list .single-comment-item .author-img {
        float: none;
        margin-bottom: 20px;
    }
}

.comments-item-list .single-comment-item .author-info-comment {
    overflow: auto;
    padding-bottom: 25px;
    border-bottom: 1px solid #e2e2e2;
}

.comments-item-list .single-comment-item .author-info-comment .info {
    position: relative;
}

.comments-item-list .single-comment-item .author-info-comment .info .theme-btn {
    padding: 7px 18px;
    font-size: 14px;
    position: absolute;
    top: 50%;
    right: 0;
    transform: translateY(-50%);
    color: #727475;
    border: 1px solid #ebebeb;
    background: transparent;
    transition: all 0.4s ease-in-out;
    box-shadow: inherit;
}

@media (max-width: 767px) {
    .comments-item-list .single-comment-item .author-info-comment .info .theme-btn {
        font-size: 12px;
    }
}

.comments-item-list .single-comment-item .author-info-comment .info .theme-btn i {
    margin-right: 5px;
}

.comments-item-list .single-comment-item .author-info-comment .info .theme-btn.minimal-btn::before,
.comments-item-list .single-comment-item .author-info-comment .info .theme-btn.minimal-btn::after {
    background-color: initial;
}

.comments-item-list .single-comment-item .author-info-comment .info .theme-btn:hover {
    background-color: var(--theme);
    color: var(--white);
}

.comments-item-list .single-comment-item .author-info-comment .info .theme-btn:hover a {
    color: var(--white) !important;
}

.comments-item-list .single-comment-item .author-info-comment .info h5 {
    margin-bottom: 5px;
}

@media (max-width: 767px) {
    .comments-item-list .single-comment-item .author-info-comment .info h5 {
        font-size: 16px;
    }
}

.comments-item-list .single-comment-item .author-info-comment span {
    color: var(--theme);
    font-weight: 500;
}

.comments-item-list .single-comment-item .author-info-comment .theme-btn {
    padding: 7px 18px;
    font-size: 14px;
    position: absolute;
    top: 50%;
    right: 0;
    transform: translateY(-50%);
}

@media (max-width: 767px) {
    .comments-item-list .single-comment-item .author-info-comment .theme-btn {
        font-size: 12px;
    }
}

.comments-item-list .single-comment-item .author-info-comment .theme-btn i {
    margin-right: 5px;
}

.comments-item-list .single-comment-item .author-info-comment .theme-btn:hover a {
    color: var(--white);
}

.comments-item-list .single-comment-item .author-info-comment .comment-text {
    margin-top: 15px;
}

.comment-form {
    background-color: var(--white);
    width: 100%;
    margin-top: 30px;
}

@media (max-width: 767px) {
    .comment-form {
        padding: 30px;
    }
}

@media (max-width: 585px) {
    .comment-form {
        padding: 30px 15px;
    }
}

.comment-form label {
    margin-bottom: 4px;
}

.comment-form input,
.comment-form textarea {
    margin-bottom: 20px;
    outline: none;
    padding: 20px 30px;
    line-height: 1;
    font-size: 16px;
    width: 100%;
    border: none;
    font-weight: 500;
    text-transform: capitalize;
    color: var(--header);
    background-color: var(--bg);
}

@media (max-width: 585px) {
    .comment-form input,
    .comment-form textarea {
        padding: 15px;
    }
}

.comment-form textarea {
    height: 200px;
    line-height: 1.3;
    border: none;
}

@media (max-width: 767px) {
    .comment-form textarea {
        height: 120px;
    }
}

.comment-form .theme-btn {
    display: inline-block;
    line-height: 1;
    padding: 20px 60px;
    transition: all 0.3s ease-in-out;
    margin-bottom: 0;
    margin-top: 20px;
}

@media (max-width: 767px) {
    .comment-form .theme-btn {
        padding: 20px 40px;
    }
}

@media (max-width: 585px) {
    .comment-form .theme-btn {
        padding: 15px 40px;
        font-size: 14px;
    }
}

.comment-form .theme-btn i {
    margin-right: 10px;
}

.comment ul {
    list-style-type: disc;
}

.comment ul ol {
    margin-bottom: 0;
}

.comment ul li {
    line-height: 1.5;
    margin-top: 10px;
    list-style-type: disc;
}

.comment ol ul {
    margin-bottom: 0;
}

.comment ol ol {
    margin-bottom: 0;
}

.comment ol li {
    line-height: 1.5;
    margin-top: 10px;
}

.site_info_widget .single-contact-info {
    overflow: auto;
    margin-bottom: 20px;
}

.site_info_widget .single-contact-info span {
    display: block;
    color: var(--theme);
}

.site_info_widget .single-contact-info .icon {
    width: 40px;
    height: 40px;
    line-height: 40px;
    color: var(--white);
    background-color: var(--theme);
    float: left;
    text-align: center;
    overflow: hidden;
    font-size: 16px;
    margin-right: 20px;
}

.site_info_widget .single-contact-info:nth-child(2n) .icon {
    background-color: var(--theme);
}

.site_info_widget .single-contact-info:nth-child(3n) .icon {
    background-color: var(--theme);
}

.site_info_widget .social-link {
    margin-top: 35px;
}

.site_info_widget .social-link a {
    margin-right: 8px !important;
}

.alignleft {
    float: left;
    margin-right: 1.5em;
    margin-bottom: 1.5em;
}

.contact-section {
    position: relative;
}

.contact-section::before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    content: "";
    background-color: var(--bg);
    z-index: -1;
    height: 70.1%;
}

.contact-section .contact-image {
    position: absolute;
    bottom: 30%;
    right: -2%;
}

@media (max-width: 1399px) {
    .contact-section .contact-image {
        display: none;
    }
}

.contact-section .contact-shape {
    position: absolute;
    right: 0;
    top: 0;
    z-index: -1;
}

@media (max-width: 1199px) {
    .contact-section .contact-shape {
        display: none;
    }
}

.contact-wrapper .contact-left-image {
    width: 915px;
    height: 600px;
    left: -50%;
    position: relative;
}

@media (max-width: 1399px) {
    .contact-wrapper .contact-left-image {
        width: 890px;
        left: -60%;
    }
}

@media (max-width: 1199px) {
    .contact-wrapper .contact-left-image {
        max-width: 915px;
        left: 0;
        width: initial;
    }
}

@media (max-width: 575px) {
    .contact-wrapper .contact-left-image {
        height: 500px;
    }
}

.contact-wrapper .contact-left-image .counter-area {
    position: absolute;
    bottom: 0;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.contact-wrapper .contact-left-image .counter-area .counter-items {
    padding: 40px 90px 40px 40px;
    background-color: var(--header);
}

@media (max-width: 767px) {
    .contact-wrapper .contact-left-image .counter-area .counter-items {
        padding: 35px;
    }
}

@media (max-width: 575px) {
    .contact-wrapper .contact-left-image .counter-area .counter-items {
        padding: 25px;
    }
}

.contact-wrapper .contact-left-image .counter-area .counter-items h2 {
    font-size: 48px;
    color: var(--white);
    margin-bottom: 5px;
}

@media (max-width: 575px) {
    .contact-wrapper .contact-left-image .counter-area .counter-items h2 {
        font-size: 42px;
    }
}

.contact-wrapper .contact-left-image .counter-area .counter-items p {
    font-size: 18px;
    color: var(--white);
}

@media (max-width: 575px) {
    .contact-wrapper .contact-left-image .counter-area .counter-items p {
        font-size: 16px;
    }
}

.contact-wrapper .contact-left-image .counter-area .counter-items.style-2 {
    background-color: var(--theme);
}

.contact-area {
    margin-left: 30px;
}

@media (max-width: 1199px) {
    .contact-area {
        margin-left: 0;
    }
}

.contact-area .contact-box {
    background-color: var(--white);
    box-shadow: var(---box-shadow);
    padding: 80px;
}

@media (max-width: 767px) {
    .contact-area .contact-box {
        padding: 50px;
    }
}

@media (max-width: 575px) {
    .contact-area .contact-box {
        padding: 40px;
    }
}

.contact-area .contact-box .contact-title {
    margin-bottom: 40px;
}

@media (max-width: 575px) {
    .contact-area .contact-box .contact-title {
        margin-bottom: 20px;
    }
}

.contact-area .contact-box .contact-title span {
    color: var(--theme);
    font-weight: 600;
    margin-bottom: 10px;
    display: inline-block;
}

.contact-area .contact-box .contact-title h3 {
    font-size: 30px;
}

.contact-area .contact-box .form-clt input,
.contact-area .contact-box .form-clt textarea {
    width: 100%;
    outline: none;
    border: none;
    padding: 18px 30px;
    color: var(--header);
    background-color: var(--bg);
    text-transform: capitalize;
}

@media (max-width: 575px) {
    .contact-area .contact-box .form-clt input,
    .contact-area .contact-box .form-clt textarea {
        padding: 14px 30px;
    }
}

.contact-area .contact-box .form-clt textarea {
    padding: 18px 30px 110px;
}

@media (max-width: 575px) {
    .contact-area .contact-box .form-clt textarea {
        padding: 14px 30px 70px;
    }
}

.contact-form-items {
    background-color: var(--bg);
    padding: 70px;
}

@media (max-width: 767px) {
    .contact-form-items {
        padding: 60px 40px;
    }
}

@media (max-width: 575px) {
    .contact-form-items {
        padding: 50px 30px;
    }
}

.contact-form-items .title {
    margin-bottom: 40px;
}

.contact-form-items .title h2 {
    font-size: 36px;
    margin-bottom: 5px;
}

.contact-form-items .form-clt input,
.contact-form-items .form-clt textarea {
    padding: 18px 30px;
    border: none;
    outline: none;
    background-color: var(--white);
    width: 100%;
    font-size: 16px;
    font-weight: 500;
    color: var(--text);
}

@media (max-width: 575px) {
    .contact-form-items .form-clt input,
    .contact-form-items .form-clt textarea {
        padding: 12px 20px;
    }
}

.contact-form-items .form-clt textarea {
    padding: 18px 30px 110px;
}

@media (max-width: 575px) {
    .contact-form-items .form-clt textarea {
        padding: 12px 20px 90px;
    }
}

.contact-form-items .form-clt::-moz-placeholder {
    color: var(--text);
}

.contact-form-items .form-clt::placeholder {
    color: var(--text);
}

.request-a-quote-section {
    position: relative;
    z-index: 2;
}

.request-a-quote-section::before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    content: "";
    background-image: var(---color-gradient-2);
    opacity: 0.8;
    z-index: -1;
}

.request-quote-form-wrapper {
    padding: 40px 50px;
    background-color: #fff;
    box-shadow: 0px 15px 40px 0px rgba(109, 109, 109, 0.06);
}

@media (max-width: 767px) {
    .request-quote-form-wrapper {
        padding: 40px 30px;
    }
}

.request-quote-form-wrapper h4,
.request-quote-form-wrapper h3 {
    margin-bottom: 25px;
    text-transform: capitalize;
}

.request-quote-form-wrapper .request-quote-form input,
.request-quote-form-wrapper .request-quote-form select {
    width: 100%;
    font-size: 15px;
    border: 1px solid #e2e2e2;
    padding: 14px 20px;
    border-radius: 0;
}

.request-quote-form-wrapper .request-quote-form label {
    font-size: 15px;
    text-transform: capitalize;
}

.request-quote-form-wrapper .request-quote-form input[type=checkbox],
.request-quote-form-wrapper .request-quote-form input[type=radio] {
    width: 15px;
    height: 15px;
    display: inline-block;
    margin-right: 8px;
    margin-bottom: 0;
    border-color: var(--theme);
}

.request-quote-form-wrapper .request-quote-form input[type=submit],
.request-quote-form-wrapper .request-quote-form .submit-btn {
    border: 0px;
    margin-top: 30px;
    margin-bottom: 10px;
    height: 60px;
    font-size: 16px;
    background-color: var(--theme);
}

@media (max-width: 767px) {
    .request-quote-form-wrapper .request-quote-form input[type=submit],
    .request-quote-form-wrapper .request-quote-form .submit-btn {
        height: 50px;
        font-size: 14px;
    }
}

.request-quote-form-wrapper .request-quote-form input[type=submit]:hover,
.request-quote-form-wrapper .request-quote-form .submit-btn:hover {
    background: var(--header);
}

.request-quote-form-wrapper .request-quote-form .nice-select {
    background-color: transparent;
    width: 100%;
    font-size: 15px;
    border: 1px solid #e2e2e2;
    padding: 14px 20px;
    border-radius: 0;
}

.single-contact-card {
    background-color: #f8f8f8;
    padding: 40px;
    transition: all 0.3s ease-in-out;
}

@media (max-width: 585px) {
    .single-contact-card {
        padding: 30px;
    }
}

.single-contact-card:hover {
    background-color: var(--theme);
}

.single-contact-card:hover .icon {
    color: #fff !important;
    background-color: var(--theme2) !important;
}

.single-contact-card:hover .bottom-part .icon {
    color: var(--theme) !important;
    background-color: #fff !important;
}

.single-contact-card:hover p,
.single-contact-card:hover span,
.single-contact-card:hover h4 {
    color: #fff;
}

.single-contact-card .top-part {
    overflow: hidden;
}

.single-contact-card .top-part .icon {
    width: 70px;
    height: 70px;
    font-size: 20px;
    line-height: 70px;
    border-radius: 50%;
    background-color: #fff;
    text-align: center;
    float: left;
    color: var(--theme);
    overflow: hidden;
    margin-right: 20px;
}

.single-contact-card .top-part .title {
    overflow: hidden;
}

.single-contact-card .bottom-part {
    margin-top: 25px;
    overflow: hidden;
}

.single-contact-card .bottom-part .icon {
    width: 50px;
    height: 50px;
    font-size: 14px;
    line-height: 50px;
    border-radius: 50%;
    background-color: #fff;
    text-align: center;
    float: right;
    overflow: hidden;
    margin-left: 20px;
}

.single-contact-card .bottom-part .info {
    display: inline-block;
    overflow: hidden;
}

.single-branch-card {
    padding: 40px;
    padding-top: 0;
    background: #fff;
    box-shadow: 0px 0px 50px 0px rgba(0, 0, 0, 0.1019607843);
    position: relative;
    margin-top: 90px;
}

.single-branch-card .country-flag {
    width: 120px;
    height: 120px;
    line-height: 120px;
    border-radius: 50%;
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1019607843);
    display: inline-block;
    margin-bottom: 20px;
    margin-top: -60px;
}

.single-branch-card .content h4 {
    position: relative;
    z-index: 1;
    display: inline-block;
    padding-right: 5px;
    margin-bottom: 20px;
    font-size: 25px;
}

.single-branch-card .content h4::before {
    position: absolute;
    content: "";
    width: 100%;
    height: 14px;
    background-color: rgba(198, 245, 255, 0.8509803922);
    left: 0;
    bottom: 2px;
    z-index: -1;
}

.single-branch-card .content p {
    margin-bottom: 10px;
}

.single-branch-card .content p a {
    font-size: inherit;
    color: var(--theme);
}

.single-branch-card .content .branch-links {
    margin-top: 15px;
}

.single-branch-card .content .branch-links a {
    text-transform: uppercase;
    font-weight: 700;
    font-size: 15px;
}

.single-branch-card .content .branch-links a:hover {
    padding-left: 5px;
    color: var(--theme3);
}

.single-branch-card .content .branch-links a i {
    margin-left: 5px;
}

.our-branch-wrapper {
    position: relative;
    z-index: 1;
}

.our-branch-wrapper .office-google-map-wrapper {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 40%;
    z-index: -1;
}

.our-branch-wrapper .office-google-map-wrapper iframe {
    width: 100%;
    height: 100%;
}

.footer-section {
    position: relative;
}

.footer-section.style-2 {
    position: relative;
}

.footer-section.style-2::before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    content: "";
    background-color: rgb(0, 5, 28);
    opacity: 0.9;
}

.footer-widgets-wrapper {
    padding: 90px 0 120px;
    position: relative;
    z-index: 9;
}

@media (max-width: 1199px) {
    .footer-widgets-wrapper {
        padding: 60px 0 90px;
    }
}

@media (max-width: 991px) {
    .footer-widgets-wrapper {
        padding: 50px 0 70px;
    }
}

.footer-widgets-wrapper .single-footer-widget {
    margin-top: 30px;
}

.footer-widgets-wrapper .single-footer-widget .widget-head {
    margin-bottom: 30px;
}

@media (max-width: 575px) {
    .footer-widgets-wrapper .single-footer-widget .widget-head {
        margin-bottom: 20px;
    }
}

.footer-widgets-wrapper .single-footer-widget .widget-head h3 {
    text-transform: capitalize;
    color: var(--white);
}

.footer-widgets-wrapper .single-footer-widget .footer-content p {
    color: var(--text2);
}

.footer-widgets-wrapper .single-footer-widget .footer-content h6 {
    margin-top: 20px;
}

.footer-widgets-wrapper .single-footer-widget .footer-content h6 a {
    color: var(--text2);
}

.footer-widgets-wrapper .single-footer-widget .footer-content .social-icon {
    margin-top: 40px;
    gap: 15px;
    position: relative;
    z-index: 9;
}

@media (max-width: 575px) {
    .footer-widgets-wrapper .single-footer-widget .footer-content .social-icon {
        margin-top: 20px;
    }
}

.footer-widgets-wrapper .single-footer-widget .footer-content .social-icon a {
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    font-size: 16px;
    display: block;
    background: rgba(92, 92, 91, 0.1);
    color: var(--text2);
    border-radius: 50%;
    transition: all 0.4s ease-in-out;
    text-align: center;
}

.footer-widgets-wrapper .single-footer-widget .footer-content .social-icon a:hover {
    background-color: var(--theme);
    color: var(--white);
}

.footer-widgets-wrapper .single-footer-widget .footer-content .footer-input {
    position: relative;
    margin-bottom: 25px;
}

.footer-widgets-wrapper .single-footer-widget .footer-content .footer-input input {
    width: 100%;
    border: none;
    background-color: transparent;
    color: var(--text2);
    position: relative;
    border-bottom: 1px solid var(--border2);
    padding-bottom: 20px;
}

.footer-widgets-wrapper .single-footer-widget .footer-content .footer-input input::-moz-placeholder {
    color: var(--text2);
}

.footer-widgets-wrapper .single-footer-widget .footer-content .footer-input input::placeholder {
    color: var(--text2);
}

.footer-widgets-wrapper .single-footer-widget .footer-content .footer-input .newsletter-button {
    position: absolute;
    right: 0;
    top: 0;
    color: var(--theme);
}

.footer-widgets-wrapper .single-footer-widget .list-area li {
    font-weight: 500;
}

.footer-widgets-wrapper .single-footer-widget .list-area li:not(:last-child) {
    margin-bottom: 15px;
}

.footer-widgets-wrapper .single-footer-widget .list-area li a {
    color: var(--text2);
    position: relative;
}

.footer-widgets-wrapper .single-footer-widget .list-area li a::before {
    position: absolute;
    top: 8px;
    left: 0;
    width: 6px;
    height: 6px;
    content: "";
    border-radius: 50%;
    background-color: var(--theme);
    opacity: 0;
    visibility: hidden;
    transition: all 0.4s ease-in-out;
}

.footer-widgets-wrapper .single-footer-widget .list-area li a:hover {
    padding-left: 20px;
    color: var(--theme);
}

.footer-widgets-wrapper .single-footer-widget .list-area li a:hover::before {
    opacity: 1;
    visibility: visible;
}

.footer-bottom {
    padding: 35px 0;
    border-top: 1px solid #363d61;
    position: relative;
    z-index: 9;
}

.footer-bottom .footer-bottom-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

@media (max-width: 575px) {
    .footer-bottom .footer-bottom-wrapper {
        flex-wrap: wrap;
        justify-content: center;
        gap: 10px;
        text-align: center;
    }
}

.footer-bottom .footer-bottom-wrapper p {
    color: var(--text2);
}

.footer-bottom .footer-bottom-wrapper p a {
    color: var(--text2);
}

.footer-bottom .footer-bottom-wrapper p a:hover {
    color: var(--theme);
}

/* custom */
a#navbarDropdown {
    color: var(--white);
    display: inline-block;
    font-size: 14px;
    font-weight: 700;
  
    border-radius: 0;
    letter-spacing: 0.2px;
    text-transform: uppercase;
    position: relative;
    z-index: 1;
}
.header-1.sticky a#navbarDropdown {
    color: #000;
}
@media(max-width:991px) {

.header-button{
display: none;
}
}

/* Optimized Language Switcher Styles */
.language-switcher {
    position: relative;
}
.language-switcher button#languageDropdown::after{
    display: none;
}
.language-switcher i.fas.fa-chevron-down.ms-1 {
    font-size: 11px;
    padding-left: 5px;
}
.language-switcher i.fas.fa-globe.me-1 {
    padding-right: 2px;
}

.language-btn {
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #fff;
    padding: 8px 16px;
    border-radius: 25px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
    display: flex;
    align-items: center;
    white-space: nowrap;
}

.language-btn:hover,
.language-btn:focus {

    background:  var(--theme);
    border-color: var(--theme);
    color: var(--white);
    text-decoration: none;
   
}

.language-btn:focus {
    outline: 2px solid var(--theme);
    outline-offset: 2px;
}

.language-switcher .dropdown-menu {
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    padding: 8px 0;
    min-width: 180px;
    margin-top: 8px;
}

.language-switcher .dropdown-item {
    padding: 10px 16px;
    font-size: 14px;
    transition: all 0.2s ease;
    border: none;
    display: flex;
    align-items: center;
}

.language-switcher .dropdown-item:hover,
.language-switcher .dropdown-item:focus {
    background-color: #f8f9fa;
    color: #007bff;
}

.language-switcher .current-language {
    background-color: #e3f2fd;
    color: #1976d2;
    font-weight: 600;
}

.language-switcher .language-option {
    cursor: pointer;
}

.language-switcher .language-name {
    flex: 1;
}

/* RTL Support */
[dir="rtl"] .language-btn {
    flex-direction: row-reverse;
}

[dir="rtl"] .language-btn .me-1 {
    margin-left: 0.25rem;
    margin-right: 0;
}

[dir="rtl"] .language-btn .ms-1 {
    margin-right: 0.25rem;
    margin-left: 0;
}

/* Mobile Responsive */
@media (max-width: 768px) {

    .language-btn {
        font-size: 13px;
        padding: 6px 12px;
    }

    .language-switcher .dropdown-menu {
        min-width: 160px;
    }
}

/* Dark theme support */
.header-1.sticky .language-btn , .static_header .language-btn {
    border-color: rgba(0, 0, 0, 0.2);
    color: #333;
}

.header-1.sticky .language-btn:hover,
.header-1.sticky .language-btn:focus ,.static_header .language-btn:hover,
.static_header .language-btn:focus {
 background:  var(--theme);
    border-color: var(--theme);
    color: var(--white);
}
.static_header .header-main .main-menu ul li.active a , .header-1.sticky .header-main .main-menu ul li.active a {
        color: var(--theme);
}
