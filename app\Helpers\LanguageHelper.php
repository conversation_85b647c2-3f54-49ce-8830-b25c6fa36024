<?php

namespace App\Helpers;

use App\Models\Language;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;

class LanguageHelper
{
    /**
     * Cache duration in minutes
     */
    const CACHE_DURATION = 60 * 24; // 24 hours

    /**
     * Get all active languages with caching
     */
    public static function getActiveLanguages()
    {
        return Cache::remember('active_languages', self::CACHE_DURATION, function () {
            return Language::where('status', 1)->get();
        });
    }

    /**
     * Get current language with caching
     */
    public static function getCurrentLanguage()
    {
        $locale = Session::get('locale', App::getLocale());
        
        return Cache::remember("current_language_{$locale}", self::CACHE_DURATION, function () use ($locale) {
            return Language::where('code', $locale)->where('status', 1)->first();
        });
    }

    /**
     * Get other languages (excluding current) with caching
     */
    public static function getOtherLanguages()
    {
        $locale = Session::get('locale', App::getLocale());
        
        return Cache::remember("other_languages_{$locale}", self::CACHE_DURATION, function () use ($locale) {
            return Language::where('code', '!=', $locale)->where('status', 1)->get();
        });
    }

    /**
     * Set application locale
     */
    public static function setLocale($locale)
    {
        // Validate if the locale exists and is active
        $language = self::getLanguageByCode($locale);
        
        if ($language) {
            Session::put('locale', $locale);
            App::setLocale($locale);
            return true;
        }
        
        return false;
    }

    /**
     * Get language by code with caching
     */
    public static function getLanguageByCode($code)
    {
        return Cache::remember("language_code_{$code}", self::CACHE_DURATION, function () use ($code) {
            return Language::where('code', $code)->where('status', 1)->first();
        });
    }

    /**
     * Get language direction (RTL/LTR)
     */
    public static function getLanguageDirection($locale = null)
    {
        $locale = $locale ?? Session::get('locale', App::getLocale());
        
        // RTL languages
        $rtlLanguages = ['ar', 'he', 'fa', 'ur'];
        
        return in_array($locale, $rtlLanguages) ? 'rtl' : 'ltr';
    }

    /**
     * Get language-specific URL
     */
    public static function getLanguageUrl($locale, $currentUrl = null)
    {
        $currentUrl = $currentUrl ?? request()->url();

        // Remove current language prefix if exists
        $path = parse_url($currentUrl, PHP_URL_PATH);
        $segments = explode('/', trim($path, '/'));

        // Check if first segment is a language code
        $languages = self::getActiveLanguages()->pluck('code')->toArray();
        if (!empty($segments[0]) && in_array($segments[0], $languages)) {
            array_shift($segments);
        }

        // Build new URL with language prefix
        $newPath = '/' . $locale . '/' . implode('/', $segments);

        return url($newPath);
    }

    /**
     * Generate URL with current language prefix
     */
    public static function route($name, $parameters = [], $absolute = true)
    {
        $locale = Session::get('locale', App::getLocale());

        // If route name doesn't start with language prefix, add it
        if (!str_starts_with($name, $locale . '.')) {
            $name = $locale . '.' . $name;
        }

        return route($name, $parameters, $absolute);
    }

    /**
     * Generate URL with language prefix for a given path
     */
    public static function url($path = '', $locale = null)
    {
        $locale = $locale ?? Session::get('locale', App::getLocale());

        // Remove leading slash if present
        $path = ltrim($path, '/');

        // Build URL with language prefix
        $languageUrl = '/' . $locale . '/' . $path;

        return url($languageUrl);
    }

    /**
     * Get current URL without language prefix
     */
    public static function getCurrentPathWithoutLanguage()
    {
        $path = request()->path();
        $segments = explode('/', $path);

        // Check if first segment is a language code
        $languages = self::getActiveLanguages()->pluck('code')->toArray();
        if (!empty($segments[0]) && in_array($segments[0], $languages)) {
            array_shift($segments);
        }

        return implode('/', $segments);
    }

    /**
     * Check if current URL has language prefix
     */
    public static function hasLanguagePrefix()
    {
        $path = request()->path();
        $segments = explode('/', $path);

        if (empty($segments[0])) {
            return false;
        }

        $languages = self::getActiveLanguages()->pluck('code')->toArray();
        return in_array($segments[0], $languages);
    }

    /**
     * Clear language cache
     */
    public static function clearCache()
    {
        $languages = Language::all();
        
        Cache::forget('active_languages');
        
        foreach ($languages as $language) {
            Cache::forget("current_language_{$language->code}");
            Cache::forget("other_languages_{$language->code}");
            Cache::forget("language_code_{$language->code}");
        }
    }

    /**
     * Get SEO meta tags for current language
     */
    public static function getSeoMetaTags()
    {
        $currentLang = self::getCurrentLanguage();
        $otherLangs = self::getOtherLanguages();
        $currentUrl = request()->url();
        
        $metaTags = [
            'lang' => $currentLang->code ?? 'en',
            'dir' => self::getLanguageDirection(),
            'hreflang' => []
        ];
        
        // Add hreflang for current language
        if ($currentLang) {
            $metaTags['hreflang'][] = [
                'hreflang' => $currentLang->code,
                'href' => $currentUrl
            ];
        }
        
        // Add hreflang for other languages
        foreach ($otherLangs as $lang) {
            $metaTags['hreflang'][] = [
                'hreflang' => $lang->code,
                'href' => self::getLanguageUrl($lang->code, $currentUrl)
            ];
        }
        
        return $metaTags;
    }

    /**
     * Get optimized translation with fallback
     */
    public static function trans($key, $parameters = [], $locale = null)
    {
        $locale = $locale ?? Session::get('locale', App::getLocale());
        
        // Try to get translation in current locale
        $translation = trans($key, $parameters, $locale);
        
        // If translation not found and not in fallback locale, try fallback
        if ($translation === $key && $locale !== config('app.fallback_locale')) {
            $translation = trans($key, $parameters, config('app.fallback_locale'));
        }
        
        return $translation;
    }
}
