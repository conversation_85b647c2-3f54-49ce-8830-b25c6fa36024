@extends('website.include.layout')
@section('meta_title', $singleArticle->seo ? json_decode($singleArticle->seo, true)['seo_title'] ??
$singleArticle->title : $singleArticle->title)
@section('meta_description', $singleArticle->seo ? json_decode($singleArticle->seo, true)['seo_description'] ??
$singleArticle->short_description : $singleArticle->short_description)
@section('meta_keywords', $singleArticle->seo ? json_decode($singleArticle->seo, true)['seo_keywords'] ??
$singleArticle->tags : $singleArticle->tags)

@section('content')




<!-- Breadcrumb-section Start -->
<section class="breadcrumb-section fix bg-cover" style="background-image: url(https://arqeengroup.com/storage/web/breadcrumb.webp);">
    <div class="container">
        <div class="row">
            <div class="page-heading">
                <ul class="breadcrumb-list wow fadeInUp" data-wow-delay=".3s">
                    <li>
                        <a href="/">Home</a>
                    </li>
                    <li>
                        Blog Details
                    </li>
                </ul>
                <h2 class="wow fadeInUp" data-wow-delay=".5s">{{ $singleArticle->category->title }}</h2>
            </div>
        </div>
    </div>
</section>

<!--<< Blog Wrapper Here >>-->
<section class="blog-wrapper  section-padding">
    <div class="container">
        <div class="news-area">
            <div class="row">
                <div class="col-12 col-lg-8">
                    <div class="blog-post-details border-wrap mt-0">
                        <div class="single-blog-post post-details mt-0">
                            <div class="post-content pt-0">
                                <h2 class="mt-0">{{ $singleArticle->title }}</h2>
                                <div class="post-meta mt-3">
                                    <span><i class="fal fa-user"></i>{{ $singleArticle->author_name ?? 'Admin' }}</span>
                                    <!-- <span><i class="fal fa-comments"></i>15 Comments</span> -->
                                    <span><i class="fal fa-calendar-alt"></i>{{ $singleArticle->created_at->format('d M, Y') }}</span>
                                </div>

                                <img loading="lazy" class="single-post-image"
                                    src="{{ json_decode($singleArticle->cover_image, true)['image'] ?? asset('website/images/blog/01.webp') }}"
                                    alt="{{ json_decode($singleArticle->cover_image, true)['alt_text'] ?? $singleArticle->title }}">

                                {!! $singleArticle->description !!}

                            </div>
                        </div>
                        <div class="row tag-share-wrap">
                            <div class="col-lg-8 col-12">
                                <h4>Releted Tags</h4>
                                <div class="tagcloud">


                                    @php
                                    // Decode tags (from either SEO or tags field)
                                    $tags = [];
                                    if (!empty($singleArticle->seo)) {
                                    $seoData = json_decode($singleArticle->seo, true);
                                    if (!empty($seoData['seo_keywords'])) {
                                    $tags = json_decode($seoData['seo_keywords'], true);
                                    }
                                    }

                                    // Fallback: use tags column if seo_keywords not found
                                    if (empty($tags) && !empty($singleArticle->tags)) {
                                    $tags = json_decode($singleArticle->tags, true);
                                    }
                                    @endphp

                                    @if(!empty($tags))
                                    @foreach($tags as $tag)

                                    <a>{{ $tag['value'] }}</a>

                                    @endforeach
                                    @else

                                    <a>No Tags</a>

                                    @endif

                                </div>
                            </div>
                            <div class="col-lg-4 col-12 mt-3 mt-lg-0 text-lg-end">
                                <h4>Social Share</h4>
                                <div class="social-share">
                                    <a href="#"><i class="fab fa-facebook-f"></i></a>
                                    <a href="#"><i class="fab fa-twitter"></i></a>
                                    <a href="#"><i class="fab fa-instagram"></i></a>
                                    <a href="#"><i class="fab fa-linkedin-in"></i></a>
                                </div>
                            </div>
                        </div>
                        <!-- comments section wrap start -->
                        <!-- <div class="comments-section-wrap pt-40">
                            <div class="comments-heading">
                                <h3>03 Comments</h3>
                            </div>
                            <ul class="comments-item-list">
                                <li class="single-comment-item">
                                    <div class="author-img">
                                        <img src="/website/img/news/author_img2.jpg" alt="img">
                                    </div>
                                    <div class="author-info-comment">
                                        <div class="info">
                                            <h5><a href="#">Rosalina Kelian</a></h5>
                                            <span>19th May 2024</span>
                                            <a href="#" class="theme-btn minimal-btn"><i class="fas fa-reply"></i>Reply</a>
                                        </div>
                                        <div class="comment-text">
                                            <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna. Ut enim ad minim veniam, quis nostrud laboris nisi ut aliquip ex ea commodo consequat.</p>
                                        </div>
                                    </div>
                                </li>
                                <li class="single-comment-item">
                                    <div class="author-img">
                                        <img src="/website/img/news/author_img3.jpg" alt="img">
                                    </div>
                                    <div class="author-info-comment">
                                        <div class="info">
                                            <h5><a href="#">Arista Williamson</a></h5>
                                            <span>21th Feb 2024</span>
                                            <a href="#" class="theme-btn minimal-btn"><i class="fas fa-reply"></i>Reply</a>
                                        </div>
                                        <div class="comment-text">
                                            <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco nisi ut aliquip ex
                                                ea commodo consequat.</p>
                                        </div>
                                    </div>
                                    <ul class="replay-comment">
                                        <li class="single-comment-item">
                                            <div class="author-img">
                                                <img src="/website/img/news/author_img4.jpg" alt="img">
                                            </div>
                                            <div class="author-info-comment">
                                                <div class="info">
                                                    <h5><a href="#">Salman Ahmed</a></h5>
                                                    <span>29th Jan 2021</span>
                                                    <a href="#" class="theme-btn minimal-btn"><i class="fas fa-reply"></i>Reply</a>
                                                </div>
                                                <div class="comment-text">
                                                    <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam..</p>
                                                </div>
                                            </div>
                                        </li>
                                    </ul>
                                </li>
                            </ul>
                        </div>
                        <div class="comment-form-wrap d-block pt-5">

                            <h3>Post Comment</h3>
                            <form action="#" class="comment-form">
                                <div class="single-form-input">
                                    <textarea placeholder="Type your comments...."></textarea>
                                </div>
                                <div class="single-form-input">
                                    <input type="text" placeholder="Type your name....">
                                </div>
                                <div class="single-form-input">
                                    <input type="email" placeholder="Type your email....">
                                </div>
                                <div class="single-form-input">
                                    <input type="text" placeholder="Type your website....">
                                </div>
                                <button class="theme-btn center" type="submit">
                                    <span><i class="fas fa-comments"></i>Post Comment</span>
                                </button>
                            </form>
                        </div> -->
                    </div>
                </div>
                <div class="col-12 col-lg-4">
                    <div class="main-sidebar">
                        <!-- <div class="single-sidebar-widget">
                            <div class="wid-title">
                                <h3>Search</h3>
                            </div>
                            <div class="search_widget">
                                <form action="#">
                                    <input type="text" placeholder="Keywords here....">
                                    <button type="submit"><i class="fal fa-search"></i></button>
                                </form>
                            </div>
                        </div> -->
                        <div class="single-sidebar-widget">
                            <div class="wid-title">
                                <h3>Popular Feeds</h3>
                            </div>
                            <div class="popular-posts">
                                @foreach($latestArticles as $latest)

                                <div class="single-post-item">
                                    <div class="thumb bg-cover">
                                        <img class="w-100 object-fit-cover h-100"
                                            loading="lazy"
                                            src="{{ json_decode($latest->cover_image, true)['image'] ?? asset('/website/images/blog/01.webp') }}"
                                            alt="{{ json_decode($latest->cover_image, true)['alt_text'] ?? $latest->title }}">
                                    </div>
                                    <div class="post-content">
                                        <h5><a href="{{route('blog.detail',$latest->slug)}}">{{ $latest->title }}</a></h5>
                                        <div class="post-date">
                                            <i class="far fa-calendar"></i>{{ $latest->created_at->format('d M, Y') }}
                                        </div>
                                    </div>
                                </div>


                                @endforeach
                            </div>
                        </div>
                        <!-- <div class="single-sidebar-widget">
                            <div class="wid-title">
                                <h3>Categories</h3>
                            </div>
                            <div class="widget_categories">
                                <ul>
                                    <li><a href="news.html">Abroad Study <span>23</span></a></li>
                                    <li><a href="news.html">Green card <span>24</span></a></li>
                                    <li><a href="news.html">PR Applicants <span>11</span></a></li>
                                    <li><a href="news.html">Travel Insurance <span>05</span></a></li>
                                    <li><a href="news.html">Visa Consultancy <span>06</span></a></li>
                                    <li><a href="news.html">Work Permits <span>10</span></a></li>
                                </ul>
                            </div>
                        </div> -->
                        <div class="single-sidebar-widget">
                            <div class="wid-title">
                                <h3>Never Miss News</h3>
                            </div>
                            <div class="social-link">
                                <a href="#"><i class="fab fa-facebook-f"></i></a>
                                <a href="#"><i class="fab fa-twitter"></i></a>
                                <a href="#"><i class="fab fa-instagram"></i></a>
                                <a href="#"><i class="fab fa-linkedin-in"></i></a>
                                <a href="#"><i class="fab fa-youtube"></i></a>
                            </div>
                        </div>
                        <!-- <div class="single-sidebar-widget">
                            <div class="wid-title">
                                <h3>Popular Tags</h3>
                            </div>
                            <div class="tagcloud">
                                <a href="news.html">Business</a>
                                <a href="news-details.html">Consulting</a>
                                <a href="news-details.html">Education</a>
                                <a href="news-details.html">Immigration</a>
                                <a href="news-details.html">Travel</a>
                                <a href="news-details.html">Visa</a>
                            </div>
                        </div> -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>



@endsection