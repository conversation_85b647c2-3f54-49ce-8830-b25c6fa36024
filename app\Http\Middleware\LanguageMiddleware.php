<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Cookie;
use App\Models\Language;

class LanguageMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Skip middleware completely for language switching routes
        $path = $request->path();
        if (str_starts_with($path, 'lang/')) {
            return $next($request);
        }

        // Get available languages
        $availableLanguages = Language::where('status', 1)->pluck('code')->toArray();
        $defaultLanguage = 'en';

        // Extract language from URL path
        $segments = explode('/', $path);
        $urlLanguage = null;

        // Check if first segment is a language code
        if (!empty($segments[0]) && in_array($segments[0], $availableLanguages)) {
            $urlLanguage = $segments[0];
        }

        // Determine the language to use
        $locale = $this->determineLanguage($request, $urlLanguage, $availableLanguages, $defaultLanguage);

        // Handle URL redirection if needed
        $redirectResponse = $this->handleUrlRedirection($request, $locale, $urlLanguage);
        if ($redirectResponse) {
            return $redirectResponse;
        }

        // Set the application locale
        App::setLocale($locale);
        Session::put('locale', $locale);

        // Store language preference in cookie (30 days)
        Cookie::queue('preferred_language', $locale, 60 * 24 * 30);

        // get current language from the database
        $curr_lang = Language::where('code', $locale)->where('status', 1)->first();

        // get all other languages
        $other_lang = Language::where('code', '!=', $locale)->where('status', 1)->get();

        View::share('cLang', $curr_lang);
        View::share('otherLangs', $other_lang);
        View::share('currentLocale', $locale);

        return $next($request);
    }

    /**
     * Determine which language to use based on URL, cookies, and defaults
     */
    private function determineLanguage($request, $urlLanguage, $availableLanguages, $defaultLanguage)
    {
        // Priority 1: URL language (if valid)
        if ($urlLanguage && in_array($urlLanguage, $availableLanguages)) {
            return $urlLanguage;
        }

        // Priority 2: Cookie preference (if valid and no URL language)
        if (!$urlLanguage) {
            $cookieLanguage = $request->cookie('preferred_language');
            if ($cookieLanguage && in_array($cookieLanguage, $availableLanguages)) {
                return $cookieLanguage;
            }
        }

        // Priority 3: Session language (if valid)
        $sessionLanguage = Session::get('locale');
        if ($sessionLanguage && in_array($sessionLanguage, $availableLanguages)) {
            return $sessionLanguage;
        }

        // Priority 4: Default language
        return $defaultLanguage;
    }

    /**
     * Handle URL redirection for language prefixes
     */
    private function handleUrlRedirection($request, $locale, $urlLanguage)
    {
        $path = $request->path();
        $queryString = $request->getQueryString();

        // Skip redirection for API routes, dashboard routes, and asset files
        if ($this->shouldSkipRedirection($path)) {
            return null;
        }

        // If URL has no language prefix and we need to add one
        if (!$urlLanguage) {
            // Redirect to the determined language (could be from cookie, session, or default)
            $cleanPath = ltrim($path, '/');
            $newPath = '/' . $locale . ($cleanPath ? '/' . $cleanPath : '');
            $newUrl = $newPath . ($queryString ? '?' . $queryString : '');
            return redirect($newUrl, 301);
        }

        // If URL has wrong language prefix, redirect to correct one
        if ($urlLanguage && $urlLanguage !== $locale) {
            $pathWithoutLang = implode('/', array_slice(explode('/', $path), 1));
            $newPath = '/' . $locale . ($pathWithoutLang ? '/' . $pathWithoutLang : '');
            $newUrl = $newPath . ($queryString ? '?' . $queryString : '');
            return redirect($newUrl, 301);
        }

        return null;
    }

    /**
     * Check if we should skip redirection for certain paths
     */
    private function shouldSkipRedirection($path)
    {
        $skipPaths = [
            'api/',
            'dashboard/',
            'admin/',
            'storage/',
            'css/',
            'js/',
            'images/',
            'img/',
            'fonts/',
            'favicon.ico',
            'robots.txt',
            'sitemap.xml',
            'lang/' // Skip language switching route
        ];

        foreach ($skipPaths as $skipPath) {
            if (str_starts_with($path, $skipPath)) {
                return true;
            }
        }

        return false;
    }
}