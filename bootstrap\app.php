<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        // Let RouteServiceProvider handle the routes
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        // Configure middleware if needed
        return $middleware;
    })
       ->withMiddleware(function (Middleware $middleware) {
        $middleware->alias(['lang'=> App\Http\Middleware\LanguageMiddleware::class,]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        // Configure exception handling if needed
        return $exceptions;
    })->create();